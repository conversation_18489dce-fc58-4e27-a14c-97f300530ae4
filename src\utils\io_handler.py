"""
IO处理工具
处理文件输入输出操作
"""

import json
import numpy as np
import cv2
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import logging
import pickle


class IOHandler:
    """IO处理器"""
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化IO处理器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 数据路径配置
        self.images_dir = Path(self.config.get('images_dir', 'data/images'))
        self.masks_dir = Path(self.config.get('masks_dir', 'data/masks'))
        self.annotations_dir = Path(self.config.get('annotations_dir', 'data/annotations'))
        self.results_dir = Path(self.config.get('results_dir', 'data/results'))
        
        # 创建目录
        for dir_path in [self.images_dir, self.masks_dir, self.annotations_dir, self.results_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def load_image(self, image_path: str) -> np.ndarray:
        """
        加载图像
        
        Args:
            image_path: 图像路径
            
        Returns:
            图像数组
        """
        try:
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Cannot load image: {image_path}")
            return image
        except Exception as e:
            self.logger.error(f"Failed to load image {image_path}: {e}")
            raise
    
    def save_image(self, image: np.ndarray, output_path: str):
        """
        保存图像
        
        Args:
            image: 图像数组
            output_path: 输出路径
        """
        try:
            cv2.imwrite(output_path, image)
            self.logger.info(f"Image saved to {output_path}")
        except Exception as e:
            self.logger.error(f"Failed to save image to {output_path}: {e}")
            raise
    
    def load_mask(self, mask_path: str) -> np.ndarray:
        """
        加载mask
        
        Args:
            mask_path: mask路径
            
        Returns:
            二值mask数组
        """
        try:
            mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
            if mask is None:
                raise ValueError(f"Cannot load mask: {mask_path}")
            
            # 确保是二值mask
            mask = (mask > 127).astype(np.uint8)
            return mask
        except Exception as e:
            self.logger.error(f"Failed to load mask {mask_path}: {e}")
            raise
    
    def save_mask(self, mask: np.ndarray, output_path: str):
        """
        保存mask
        
        Args:
            mask: mask数组
            output_path: 输出路径
        """
        try:
            # 确保是0-255范围
            mask_to_save = (mask * 255).astype(np.uint8)
            cv2.imwrite(output_path, mask_to_save)
            self.logger.info(f"Mask saved to {output_path}")
        except Exception as e:
            self.logger.error(f"Failed to save mask to {output_path}: {e}")
            raise
    
    def load_json(self, json_path: str) -> Dict:
        """
        加载JSON文件
        
        Args:
            json_path: JSON文件路径
            
        Returns:
            JSON数据字典
        """
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data
        except Exception as e:
            self.logger.error(f"Failed to load JSON {json_path}: {e}")
            raise
    
    def save_json(self, data: Dict, output_path: str):
        """
        保存JSON文件
        
        Args:
            data: 要保存的数据
            output_path: 输出路径
        """
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False, default=self._json_serializer)
            self.logger.info(f"JSON saved to {output_path}")
        except Exception as e:
            self.logger.error(f"Failed to save JSON to {output_path}: {e}")
            raise
    
    def _json_serializer(self, obj):
        """JSON序列化器，处理numpy类型"""
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
    
    def load_annotation(self, annotation_path: str) -> Dict:
        """
        加载标注文件
        
        Args:
            annotation_path: 标注文件路径
            
        Returns:
            标注数据
        """
        return self.load_json(annotation_path)
    
    def save_annotation(self, annotation: Dict, output_path: str):
        """
        保存标注文件
        
        Args:
            annotation: 标注数据
            output_path: 输出路径
        """
        self.save_json(annotation, output_path)
    
    def load_dataset_info(self, dataset_path: str) -> Dict:
        """
        加载数据集信息
        
        Args:
            dataset_path: 数据集路径
            
        Returns:
            数据集信息
        """
        info_file = Path(dataset_path) / "dataset_info.json"
        if info_file.exists():
            return self.load_json(str(info_file))
        else:
            # 扫描目录生成数据集信息
            return self._scan_dataset(dataset_path)
    
    def _scan_dataset(self, dataset_path: str) -> Dict:
        """
        扫描数据集目录
        
        Args:
            dataset_path: 数据集路径
            
        Returns:
            数据集信息
        """
        dataset_dir = Path(dataset_path)
        
        # 查找图像文件
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        image_files = []
        
        for ext in image_extensions:
            image_files.extend(list(dataset_dir.glob(f"**/*{ext}")))
            image_files.extend(list(dataset_dir.glob(f"**/*{ext.upper()}")))
        
        # 生成数据集信息
        dataset_info = {
            'dataset_path': str(dataset_dir),
            'total_images': len(image_files),
            'image_files': [str(f.relative_to(dataset_dir)) for f in image_files],
            'extensions': list(set(f.suffix.lower() for f in image_files))
        }
        
        return dataset_info
    
    def create_data_structure(self, base_path: str, structure: Dict):
        """
        创建数据目录结构
        
        Args:
            base_path: 基础路径
            structure: 目录结构字典
        """
        base_dir = Path(base_path)
        
        def create_dirs(current_path: Path, struct: Dict):
            for name, content in struct.items():
                new_path = current_path / name
                new_path.mkdir(parents=True, exist_ok=True)
                
                if isinstance(content, dict):
                    create_dirs(new_path, content)
        
        create_dirs(base_dir, structure)
        self.logger.info(f"Data structure created at {base_path}")
    
    def batch_load_masks(self, mask_dir: str, pattern: str = "*.png") -> Dict[str, np.ndarray]:
        """
        批量加载mask文件
        
        Args:
            mask_dir: mask目录
            pattern: 文件模式
            
        Returns:
            {文件名: mask} 字典
        """
        mask_path = Path(mask_dir)
        mask_files = list(mask_path.glob(pattern))
        
        masks = {}
        for mask_file in mask_files:
            try:
                mask = self.load_mask(str(mask_file))
                masks[mask_file.stem] = mask
            except Exception as e:
                self.logger.warning(f"Failed to load mask {mask_file}: {e}")
                continue
        
        self.logger.info(f"Loaded {len(masks)} masks from {mask_dir}")
        return masks
    
    def batch_save_masks(self, masks: Dict[str, np.ndarray], output_dir: str):
        """
        批量保存mask文件
        
        Args:
            masks: {文件名: mask} 字典
            output_dir: 输出目录
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        for name, mask in masks.items():
            output_file = output_path / f"{name}.png"
            try:
                self.save_mask(mask, str(output_file))
            except Exception as e:
                self.logger.error(f"Failed to save mask {name}: {e}")
                continue
        
        self.logger.info(f"Saved {len(masks)} masks to {output_dir}")
    
    def export_results(self, results: List[Dict], output_dir: str):
        """
        导出处理结果
        
        Args:
            results: 结果列表
            output_dir: 输出目录
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 保存汇总报告
        summary = self._create_summary_report(results)
        self.save_json(summary, str(output_path / "summary_report.json"))
        
        # 保存详细结果
        for i, result in enumerate(results):
            result_dir = output_path / f"result_{i:04d}"
            result_dir.mkdir(exist_ok=True)
            
            # 保存各种数据
            if 'completed_mask' in result:
                self.save_mask(result['completed_mask'], 
                             str(result_dir / "completed_mask.png"))
            
            if 'completion_regions' in result:
                self.save_mask(result['completion_regions'], 
                             str(result_dir / "completion_regions.png"))
            
            if 'report' in result:
                self.save_json(result['report'], 
                             str(result_dir / "report.json"))
        
        self.logger.info(f"Results exported to {output_dir}")
    
    def _create_summary_report(self, results: List[Dict]) -> Dict:
        """
        创建汇总报告
        
        Args:
            results: 结果列表
            
        Returns:
            汇总报告
        """
        if not results:
            return {'total_results': 0}
        
        # 统计信息
        total_results = len(results)
        successful_results = sum(1 for r in results if r.get('statistics', {}).get('successful_completions', 0) > 0)
        
        # 平均指标
        avg_confidence = np.mean([r.get('report', {}).get('confidence', {}).get('mean_confidence', 0) 
                                for r in results])
        
        total_completed_pixels = sum(r.get('statistics', {}).get('total_completed_pixels', 0) 
                                   for r in results)
        
        summary = {
            'total_results': total_results,
            'successful_results': successful_results,
            'success_rate': successful_results / total_results if total_results > 0 else 0,
            'average_confidence': float(avg_confidence),
            'total_completed_pixels': int(total_completed_pixels),
            'processing_statistics': {
                'total_inferences': sum(r.get('statistics', {}).get('total_inferences', 0) 
                                      for r in results),
                'successful_completions': sum(r.get('statistics', {}).get('successful_completions', 0) 
                                            for r in results),
                'failed_completions': sum(r.get('statistics', {}).get('failed_completions', 0) 
                                        for r in results)
            }
        }
        
        return summary
