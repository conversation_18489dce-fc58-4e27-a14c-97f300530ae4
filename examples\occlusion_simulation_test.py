"""
遮挡模拟测试
通过对比完整标线和遮挡后的标线来模拟真实的遮挡检测
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import cv2
from src.models.mask_model import Lane<PERSON><PERSON>, LaneType, OcclusionType
from src.core.occlusion_detector import OcclusionDetector
from src.core.rule_engine import RuleEngine
from src.core.structure_completer import StructureCompleter
from src.utils.mask_processor import MaskProcessor


def create_occlusion_simulation():
    """创建遮挡模拟场景"""
    height, width = 480, 640

    # 创建背景图像
    image = np.ones((height, width, 3), dtype=np.uint8) * 80
    cv2.rectangle(image, (0, height//3), (width, 2*height//3), (60, 60, 60), -1)

    # 1. 创建完整的标线mask
    complete_mask = np.zeros((height, width), dtype=np.uint8)

    # 单实线
    y_line = height // 2
    cv2.line(complete_mask, (50, y_line), (width-50, y_line), 3, 4)

    # 虚线
    y_dashed = height // 2 - 50
    for x in range(50, width-50, 40):
        cv2.line(complete_mask, (x, y_dashed), (x+20, y_dashed), 4, 3)

    # 停止线
    y_stop = height // 2 - 100
    cv2.line(complete_mask, (200, y_stop), (450, y_stop), 6, 8)

    # 直行箭头
    arrow_x, arrow_y = 300, height // 2 + 80
    arrow_points = np.array([
        [arrow_x, arrow_y + 30],
        [arrow_x + 15, arrow_y + 15],
        [arrow_x + 8, arrow_y + 15],
        [arrow_x + 8, arrow_y - 15],
        [arrow_x - 8, arrow_y - 15],
        [arrow_x - 8, arrow_y + 15],
        [arrow_x - 15, arrow_y + 15]
    ], np.int32)
    cv2.fillPoly(complete_mask, [arrow_points], 7)

    # 2. 创建遮挡区域
    occlusion_regions = {}

    # 车辆遮挡单实线
    car_mask = np.zeros((height, width), dtype=np.uint8)
    cv2.rectangle(car_mask, (250, y_line - 30), (400, y_line + 30), 1, -1)
    occlusion_regions['car'] = car_mask

    # 行人遮挡箭头
    human_mask = np.zeros((height, width), dtype=np.uint8)
    cv2.ellipse(human_mask, (arrow_x - 10, arrow_y), (25, 40), 0, 0, 360, 1, -1)
    occlusion_regions['human'] = human_mask

    # 3. 创建被遮挡后的mask
    occluded_mask = complete_mask.copy()

    # 移除被遮挡的标线部分
    for name, occ_mask in occlusion_regions.items():
        occluded_mask[occ_mask > 0] = 0

    return image, complete_mask, occluded_mask, occlusion_regions


def simulate_occlusion_detection(complete_mask, occluded_mask, occlusion_regions):
    """模拟遮挡检测过程"""

    # 1. 解析完整标线
    mask_processor = MaskProcessor()
    complete_lanes, complete_types, _, _ = mask_processor.parse_grayscale_mask(complete_mask)

    # 2. 解析被遮挡后的标线
    occluded_lanes, occluded_types, _, _ = mask_processor.parse_grayscale_mask(occluded_mask)

    # 3. 通过对比找出被遮挡的区域
    detected_occlusions = {}

    for name, complete_lane_mask in complete_lanes.items():
        if name in occluded_lanes:
            occluded_lane_mask = occluded_lanes[name]

            # 找出缺失的部分
            missing_parts = complete_lane_mask - occluded_lane_mask

            if np.sum(missing_parts) > 0:
                print(f"检测到标线 {name} 有 {np.sum(missing_parts)} 像素被遮挡")

                # 找出与哪个遮挡物重合
                for occ_name, occ_mask in occlusion_regions.items():
                    overlap = np.logical_and(missing_parts, occ_mask)
                    if np.sum(overlap) > 0:
                        overlap_ratio = np.sum(overlap) / np.sum(complete_lane_mask)
                        print(f"  - 与 {occ_name} 重合 {overlap_ratio:.2%}")

                        if occ_name not in detected_occlusions:
                            detected_occlusions[occ_name] = []

                        detected_occlusions[occ_name].append({
                            'lane_name': name,
                            'lane_type': complete_types[name],
                            'missing_mask': missing_parts,
                            'overlap_ratio': overlap_ratio
                        })

    return detected_occlusions


def run_occlusion_simulation_test():
    """运行遮挡模拟测试"""
    print("=== 遮挡模拟测试 ===")

    # 1. 创建测试场景
    print("1. 创建遮挡模拟场景...")
    image, complete_mask, occluded_mask, occlusion_regions = create_occlusion_simulation()

    print(f"   完整标线唯一像素值: {np.unique(complete_mask)}")
    print(f"   遮挡后标线唯一像素值: {np.unique(occluded_mask)}")

    # 2. 模拟遮挡检测
    print("2. 模拟遮挡检测...")
    detected_occlusions = simulate_occlusion_detection(complete_mask, occluded_mask, occlusion_regions)

    # 3. 使用检测到的遮挡信息进行规则推理
    print("3. 基于检测结果进行规则推理...")

    # 初始化处理器（降低置信度阈值用于测试）
    mask_processor = MaskProcessor()
    detector = OcclusionDetector({'confidence_threshold': 0.2})  # 降低阈值
    engine = RuleEngine()
    completer = StructureCompleter()

    # 解析完整的标线（用于遮挡检测）
    complete_lanes, complete_types, _, _ = mask_processor.parse_grayscale_mask(complete_mask)

    # 创建标线mask对象（使用完整标线）
    lane_mask = LaneMask(image.shape[:2])
    for name, mask in complete_lanes.items():
        lane_type = complete_types[name]
        lane_id = lane_mask.add_lane_segment(lane_type, mask)
        print(f"   添加完整标线段: {name} (ID: {lane_id})")

    # 添加检测到的遮挡区域
    occlusion_masks = {}
    occlusion_types = {}

    for occ_name, occ_mask in occlusion_regions.items():
        if occ_name in detected_occlusions:
            occlusion_masks[occ_name] = occ_mask
            occlusion_types[occ_name] = OcclusionType.DYNAMIC

    # 执行遮挡检测
    occlusion_analyses = detector.detect_occlusions(
        lane_mask, occlusion_masks, occlusion_types
    )

    print(f"   检测到 {len(occlusion_analyses)} 个遮挡区域")
    for analysis in occlusion_analyses:
        lane_segment = lane_mask.lane_segments[analysis.lane_id]
        print(f"   - 标线 {analysis.lane_id} ({lane_segment.lane_type.value}) "
              f"被 {analysis.occlusion_type.value} 遮挡 {analysis.overlap_ratio:.2%}, "
              f"置信度: {analysis.confidence:.3f}, 面积: {analysis.occlusion_area}")

    # 过滤显著遮挡
    significant_occlusions = detector.filter_significant_occlusions(occlusion_analyses)
    print(f"   其中 {len(significant_occlusions)} 个为显著遮挡")

    # 显示过滤条件
    print(f"   过滤条件: overlap_ratio >= {detector.overlap_threshold}, "
          f"area >= {detector.min_occlusion_area}, "
          f"confidence >= {detector.confidence_threshold}")

    # 创建上下文信息
    contexts = []
    for analysis in significant_occlusions:
        lane_segment = lane_mask.lane_segments[analysis.lane_id]
        context = detector.create_occlusion_context(lane_segment, analysis, lane_mask)
        contexts.append(context)
        print(f"   上下文 - 标线 {analysis.lane_id}: 类型={context['lane_type'].value}, "
              f"遮挡比例={context['occlusion_ratio']:.2%}, "
              f"可见长度={context['visible_length']}, "
              f"方向={context['direction']}")

    # 规则推理
    inference_results = engine.infer_completions(lane_mask, significant_occlusions, contexts)
    print(f"   生成 {len(inference_results)} 个推理结果")

    for result in inference_results:
        lane_segment = lane_mask.lane_segments[result.lane_id]
        print(f"   - 规则 {result.rule_id}: 标线 {result.lane_id} ({lane_segment.lane_type.value}), "
              f"置信度: {result.confidence:.3f}")

    # 结构补全
    completion_result = completer.complete_structure(lane_mask, inference_results)

    print("   补全统计:")
    stats = completion_result.statistics
    print(f"   - 成功补全: {stats['successful_completions']}")
    print(f"   - 补全像素: {stats['total_completed_pixels']}")

    # 4. 生成可视化和保存结果
    print("4. 生成可视化结果...")

    output_dir = "examples/output_simulation"
    os.makedirs(output_dir, exist_ok=True)

    # 保存各种mask
    cv2.imwrite(f"{output_dir}/complete_mask.png", complete_mask)
    cv2.imwrite(f"{output_dir}/occluded_mask.png", occluded_mask)

    # 保存遮挡区域
    for name, mask in occlusion_regions.items():
        cv2.imwrite(f"{output_dir}/occlusion_{name}.png", mask * 255)

    # 保存检测到的缺失部分
    for occ_name, detections in detected_occlusions.items():
        for i, detection in enumerate(detections):
            missing_mask = detection['missing_mask']
            cv2.imwrite(f"{output_dir}/missing_{occ_name}_{detection['lane_name']}.png",
                       missing_mask * 255)

    # 创建对比可视化
    comparison_rgb = mask_processor.create_detailed_rgb_visualization(
        lane_mask, completion_result.completed_mask, completion_result.completion_regions
    )
    cv2.imwrite(f"{output_dir}/comparison_visualization.jpg", comparison_rgb)

    # 创建完整vs遮挡对比
    h, w = complete_mask.shape
    comparison_masks = np.zeros((h, w * 2), dtype=np.uint8)
    comparison_masks[:, :w] = complete_mask
    comparison_masks[:, w:] = occluded_mask
    cv2.imwrite(f"{output_dir}/complete_vs_occluded.png", comparison_masks)

    print(f"   结果已保存到: {output_dir}")

    # 5. 显示总结
    print("\n=== 遮挡模拟测试总结 ===")
    print(f"检测结果:")
    for occ_name, detections in detected_occlusions.items():
        print(f"  {occ_name} 遮挡了 {len(detections)} 个标线:")
        for detection in detections:
            print(f"    - {detection['lane_name']} ({detection['lane_type'].value}): "
                  f"{detection['overlap_ratio']:.2%}")

    print(f"推理结果:")
    print(f"  - 匹配规则: {len(inference_results)}")
    print(f"  - 成功补全: {stats['successful_completions']}")
    print(f"  - 补全像素: {stats['total_completed_pixels']}")

    print("\n遮挡模拟测试完成！")
    return completion_result, detected_occlusions


if __name__ == "__main__":
    try:
        completion_result, detected_occlusions = run_occlusion_simulation_test()
        print("\n测试运行成功！")

    except Exception as e:
        print(f"测试运行出错: {e}")
        import traceback
        traceback.print_exc()
