"""
测试mask数据模型
"""

import pytest
import numpy as np
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.models.mask_model import (
    LaneMask, LaneSegment, OcclusionRegion, BoundingBox,
    LaneType, OcclusionType
)


class TestBoundingBox:
    """测试边界框"""

    def test_bbox_properties(self):
        """测试边界框属性"""
        bbox = BoundingBox(10, 20, 50, 80)

        assert bbox.width == 40
        assert bbox.height == 60
        assert bbox.area == 2400
        assert bbox.center == (30, 50)

    def test_bbox_edge_cases(self):
        """测试边界框边界情况"""
        # 零尺寸
        bbox = BoundingBox(10, 10, 10, 10)
        assert bbox.width == 0
        assert bbox.height == 0
        assert bbox.area == 0


class TestLaneSegment:
    """测试标线段"""

    def setup_method(self):
        """设置测试数据"""
        # 创建一个简单的水平线mask
        self.mask = np.zeros((100, 100), dtype=np.uint8)
        self.mask[45:55, 10:90] = 1  # 水平线

        self.bbox = BoundingBox(10, 45, 90, 55)
        self.pixels = [(x, y) for y in range(45, 55) for x in range(10, 90)]

        self.lane_segment = LaneSegment(
            lane_id=1,
            lane_type=LaneType.SINGLE_SOLID,
            mask=self.mask,
            bbox=self.bbox,
            confidence=0.9,
            pixels=self.pixels
        )

    def test_lane_segment_creation(self):
        """测试标线段创建"""
        assert self.lane_segment.lane_id == 1
        assert self.lane_segment.lane_type == LaneType.SINGLE_SOLID
        assert self.lane_segment.confidence == 0.9
        assert len(self.lane_segment.pixels) > 0

    def test_get_skeleton(self):
        """测试骨架提取"""
        skeleton = self.lane_segment.get_skeleton()
        assert skeleton.shape == self.mask.shape
        assert np.any(skeleton)  # 应该有骨架点

    def test_get_endpoints(self):
        """测试端点检测"""
        endpoints = self.lane_segment.get_endpoints()
        assert isinstance(endpoints, list)
        # 水平线应该有两个端点
        assert len(endpoints) >= 0  # 简化的骨架算法可能不准确

    def test_get_direction_vector(self):
        """测试方向向量"""
        direction = self.lane_segment.get_direction_vector()
        assert direction is not None
        assert len(direction) == 2
        # 水平线的主方向应该接近(1, 0)
        assert abs(direction[0]) > abs(direction[1])


class TestOcclusionRegion:
    """测试遮挡区域"""

    def setup_method(self):
        """设置测试数据"""
        self.mask = np.zeros((100, 100), dtype=np.uint8)
        self.mask[40:60, 30:70] = 1  # 矩形遮挡

        self.bbox = BoundingBox(30, 40, 70, 60)

        self.occlusion_region = OcclusionRegion(
            occlusion_id=1,
            occlusion_type=OcclusionType.DYNAMIC,
            mask=self.mask,
            bbox=self.bbox,
            confidence=0.8,
            affected_lanes=[1, 2]
        )

    def test_occlusion_region_creation(self):
        """测试遮挡区域创建"""
        assert self.occlusion_region.occlusion_id == 1
        assert self.occlusion_region.occlusion_type == OcclusionType.DYNAMIC
        assert self.occlusion_region.confidence == 0.8
        assert self.occlusion_region.affected_lanes == [1, 2]

    def test_get_overlap_with_lane(self):
        """测试与标线的重合度计算"""
        # 创建一个与遮挡区域部分重合的标线
        lane_mask = np.zeros((100, 100), dtype=np.uint8)
        lane_mask[45:55, 20:80] = 1  # 水平线，与遮挡区域重合

        lane_segment = LaneSegment(
            lane_id=1,
            lane_type=LaneType.SINGLE_SOLID,
            mask=lane_mask,
            bbox=BoundingBox(20, 45, 80, 55),
            confidence=0.9,
            pixels=[]
        )

        overlap = self.occlusion_region.get_overlap_with_lane(lane_segment)
        assert 0 <= overlap <= 1
        assert overlap > 0  # 应该有重合


class TestLaneMask:
    """测试标线mask主类"""

    def setup_method(self):
        """设置测试数据"""
        self.lane_mask = LaneMask((100, 100))

    def test_lane_mask_creation(self):
        """测试标线mask创建"""
        assert self.lane_mask.height == 100
        assert self.lane_mask.width == 100
        assert len(self.lane_mask.lane_segments) == 0
        assert len(self.lane_mask.occlusion_regions) == 0
        assert self.lane_mask.next_lane_id == 1
        assert self.lane_mask.next_occlusion_id == 1

    def test_add_lane_segment(self):
        """测试添加标线段"""
        mask = np.zeros((100, 100), dtype=np.uint8)
        mask[45:55, 10:90] = 1

        lane_id = self.lane_mask.add_lane_segment(
            LaneType.SINGLE_SOLID, mask, confidence=0.9
        )

        assert lane_id == 1
        assert len(self.lane_mask.lane_segments) == 1
        assert self.lane_mask.next_lane_id == 2

        lane_segment = self.lane_mask.lane_segments[lane_id]
        assert lane_segment.lane_type == LaneType.SINGLE_SOLID
        assert lane_segment.confidence == 0.9

    def test_add_lane_segment_empty_mask(self):
        """测试添加空mask"""
        empty_mask = np.zeros((100, 100), dtype=np.uint8)

        with pytest.raises(ValueError):
            self.lane_mask.add_lane_segment(LaneType.SINGLE_SOLID, empty_mask)

    def test_add_occlusion_region(self):
        """测试添加遮挡区域"""
        # 先添加一个标线段
        lane_mask = np.zeros((100, 100), dtype=np.uint8)
        lane_mask[45:55, 10:90] = 1
        lane_id = self.lane_mask.add_lane_segment(LaneType.SINGLE_SOLID, lane_mask)

        # 添加遮挡区域
        occlusion_mask = np.zeros((100, 100), dtype=np.uint8)
        occlusion_mask[40:60, 30:70] = 1  # 与标线重合

        occlusion_id = self.lane_mask.add_occlusion_region(
            OcclusionType.DYNAMIC, occlusion_mask, confidence=0.8
        )

        assert occlusion_id == 1
        assert len(self.lane_mask.occlusion_regions) == 1
        assert self.lane_mask.next_occlusion_id == 2

        occlusion_region = self.lane_mask.occlusion_regions[occlusion_id]
        assert occlusion_region.occlusion_type == OcclusionType.DYNAMIC
        assert lane_id in occlusion_region.affected_lanes

    def test_get_combined_mask(self):
        """测试获取组合mask"""
        # 添加两个标线段
        mask1 = np.zeros((100, 100), dtype=np.uint8)
        mask1[20:30, 10:90] = 1
        self.lane_mask.add_lane_segment(LaneType.SINGLE_SOLID, mask1)

        mask2 = np.zeros((100, 100), dtype=np.uint8)
        mask2[70:80, 10:90] = 1
        self.lane_mask.add_lane_segment(LaneType.DASHED, mask2)

        # 获取组合mask
        combined = self.lane_mask.get_combined_mask()

        assert combined.shape == (100, 100)
        assert np.any(combined[20:30, 10:90])  # 第一个标线
        assert np.any(combined[70:80, 10:90])  # 第二个标线

    def test_get_combined_mask_with_filter(self):
        """测试按类型过滤的组合mask"""
        # 添加不同类型的标线段
        mask1 = np.zeros((100, 100), dtype=np.uint8)
        mask1[20:30, 10:90] = 1
        self.lane_mask.add_lane_segment(LaneType.SINGLE_SOLID, mask1)

        mask2 = np.zeros((100, 100), dtype=np.uint8)
        mask2[70:80, 10:90] = 1
        self.lane_mask.add_lane_segment(LaneType.DASHED, mask2)

        # 只获取单实线
        solid_only = self.lane_mask.get_combined_mask([LaneType.SINGLE_SOLID])

        assert np.any(solid_only[20:30, 10:90])  # 应该包含单实线
        assert not np.any(solid_only[70:80, 10:90])  # 不应该包含虚线

    def test_get_visible_mask(self):
        """测试获取可见标线mask"""
        # 添加标线段
        lane_mask = np.zeros((100, 100), dtype=np.uint8)
        lane_mask[45:55, 10:90] = 1
        self.lane_mask.add_lane_segment(LaneType.SINGLE_SOLID, lane_mask)

        # 添加遮挡区域（只覆盖标线的一部分）
        occlusion_mask = np.zeros((100, 100), dtype=np.uint8)
        occlusion_mask[45:55, 30:70] = 1  # 只覆盖标线区域
        self.lane_mask.add_occlusion_region(OcclusionType.DYNAMIC, occlusion_mask)

        # 获取可见mask
        visible = self.lane_mask.get_visible_mask()

        assert visible.shape == (100, 100)
        # 在遮挡区域外应该可见
        assert np.any(visible[45:55, 10:30])  # 遮挡区域左侧
        assert np.any(visible[45:55, 70:90])  # 遮挡区域右侧
        # 在遮挡区域内应该不可见
        assert not np.any(visible[45:55, 30:70])  # 遮挡区域内


if __name__ == "__main__":
    pytest.main([__file__])
