"""
真实遮挡测试示例
创建更真实的遮挡场景，测试规则推理补全效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import cv2
from src.models.mask_model import Lane<PERSON>ask, LaneType, OcclusionType
from src.core.occlusion_detector import OcclusionDetector
from src.core.rule_engine import RuleEngine
from src.core.structure_completer import StructureCompleter
from src.utils.mask_processor import MaskProcessor
from src.utils.visualization import Visualizer


def create_realistic_test_scene():
    """创建真实的测试场景"""
    height, width = 480, 640

    # 创建背景图像
    image = np.ones((height, width, 3), dtype=np.uint8) * 80
    cv2.rectangle(image, (0, height//3), (width, 2*height//3), (60, 60, 60), -1)

    # 创建灰度mask - 先创建完整的标线
    mask = np.zeros((height, width), dtype=np.uint8)

    # 1. 创建连续的单实线
    y_line = height // 2
    cv2.line(mask, (50, y_line), (width-50, y_line), 3, 4)

    # 2. 创建虚线
    y_dashed = height // 2 - 50
    for x in range(50, width-50, 40):
        cv2.line(mask, (x, y_dashed), (x+20, y_dashed), 4, 3)

    # 3. 创建停止线
    y_stop = height // 2 - 100
    cv2.line(mask, (200, y_stop), (450, y_stop), 6, 8)

    # 4. 创建直行箭头
    arrow_x, arrow_y = 300, height // 2 + 80
    arrow_points = np.array([
        [arrow_x, arrow_y + 30],
        [arrow_x + 15, arrow_y + 15],
        [arrow_x + 8, arrow_y + 15],
        [arrow_x + 8, arrow_y - 15],
        [arrow_x - 8, arrow_y - 15],
        [arrow_x - 8, arrow_y + 15],
        [arrow_x - 15, arrow_y + 15]
    ], np.int32)
    cv2.fillPoly(mask, [arrow_points], 7)

    # 5. 创建导流区
    for i in range(0, 80, 8):
        cv2.line(mask, (500 + i, height//2 + 60), (500 + i + 30, height//2 + 90), 8, 2)

    # 保存完整的标线mask
    complete_lanes_mask = mask.copy()

    # 创建遮挡区域mask（单独的）
    occlusion_mask = np.zeros((height, width), dtype=np.uint8)

    # 车辆遮挡 - 覆盖单实线的一部分
    car_x1, car_x2 = 250, 400
    car_y1, car_y2 = y_line - 30, y_line + 30
    cv2.rectangle(occlusion_mask, (car_x1, car_y1), (car_x2, car_y2), 1, -1)

    # 行人遮挡 - 覆盖箭头的一部分
    cv2.ellipse(occlusion_mask, (arrow_x - 10, arrow_y), (25, 40), 0, 0, 360, 2, -1)

    # 另一个车辆遮挡 - 覆盖停止线
    cv2.rectangle(occlusion_mask, (300, y_stop - 15), (380, y_stop + 15), 1, -1)

    # 创建最终的mask：在遮挡区域，用遮挡物像素值替换标线像素值
    final_mask = complete_lanes_mask.copy()
    final_mask[occlusion_mask > 0] = occlusion_mask[occlusion_mask > 0]

    return image, final_mask, complete_lanes_mask, occlusion_mask


def run_realistic_occlusion_test():
    """运行真实遮挡测试"""
    print("=== 真实遮挡场景测试 ===")

    # 1. 创建测试数据
    print("1. 创建真实遮挡测试场景...")
    image, grayscale_mask = create_realistic_test_scene()

    print(f"   唯一像素值: {np.unique(grayscale_mask)}")

    # 2. 初始化处理器
    print("2. 初始化处理器...")
    mask_processor = MaskProcessor()
    detector = OcclusionDetector()
    engine = RuleEngine()
    completer = StructureCompleter()

    # 3. 解析灰度mask
    print("3. 解析灰度mask...")
    lane_masks, lane_types, occlusion_masks, occlusion_types = mask_processor.parse_grayscale_mask(grayscale_mask)

    print(f"   检测到标线类型: {len(lane_masks)}")
    for name, lane_type in lane_types.items():
        pixels = np.sum(lane_masks[name])
        print(f"   - {name}: {lane_type.value} ({pixels} 像素)")

    print(f"   检测到遮挡物: {len(occlusion_masks)}")
    for name, occlusion_type in occlusion_types.items():
        pixels = np.sum(occlusion_masks[name])
        print(f"   - {name}: {occlusion_type.value} ({pixels} 像素)")

    # 4. 创建标线mask对象
    print("4. 创建标线mask对象...")
    lane_mask = LaneMask(image.shape[:2])

    for name, mask in lane_masks.items():
        lane_type = lane_types[name]
        lane_id = lane_mask.add_lane_segment(lane_type, mask)
        print(f"   添加标线段: {name} (ID: {lane_id})")

    # 5. 检测遮挡
    print("5. 检测遮挡...")
    occlusion_analyses = detector.detect_occlusions(
        lane_mask, occlusion_masks, occlusion_types
    )

    print(f"   检测到 {len(occlusion_analyses)} 个遮挡区域")
    for analysis in occlusion_analyses:
        lane_segment = lane_mask.lane_segments[analysis.lane_id]
        print(f"   - 标线 {analysis.lane_id} ({lane_segment.lane_type.value}) "
              f"被 {analysis.occlusion_type.value} 遮挡 {analysis.overlap_ratio:.2%}, "
              f"置信度: {analysis.confidence:.3f}")

    # 过滤显著遮挡
    significant_occlusions = detector.filter_significant_occlusions(occlusion_analyses)
    print(f"   其中 {len(significant_occlusions)} 个为显著遮挡")

    # 6. 创建上下文信息
    print("6. 创建推理上下文...")
    contexts = []
    for analysis in significant_occlusions:
        lane_segment = lane_mask.lane_segments[analysis.lane_id]
        context = detector.create_occlusion_context(lane_segment, analysis, lane_mask)
        contexts.append(context)
        print(f"   标线 {analysis.lane_id}: 类型={context['lane_type'].value}, "
              f"遮挡比例={context['occlusion_ratio']:.2%}, "
              f"可见长度={context['visible_length']}")

    # 7. 规则推理
    print("7. 执行规则推理...")
    inference_results = engine.infer_completions(lane_mask, significant_occlusions, contexts)

    print(f"   生成 {len(inference_results)} 个推理结果")
    for result in inference_results:
        lane_segment = lane_mask.lane_segments[result.lane_id]
        print(f"   - 规则 {result.rule_id}: 标线 {result.lane_id} ({lane_segment.lane_type.value}), "
              f"置信度: {result.confidence:.3f}")

    # 8. 结构补全
    print("8. 执行结构补全...")
    completion_result = completer.complete_structure(lane_mask, inference_results)

    print("   补全统计:")
    stats = completion_result.statistics
    print(f"   - 总推理数: {stats['total_inferences']}")
    print(f"   - 成功补全: {stats['successful_completions']}")
    print(f"   - 失败补全: {stats['failed_completions']}")
    print(f"   - 补全像素: {stats['total_completed_pixels']}")
    if stats['successful_completions'] > 0:
        print(f"   - 平均置信度: {stats['average_confidence']:.3f}")
        print(f"   - 按类型补全: {stats['completion_by_type']}")

    # 9. 生成可视化
    print("9. 生成可视化结果...")

    # 创建详细的RGB可视化
    rgb_detailed = mask_processor.create_detailed_rgb_visualization(
        lane_mask, completion_result.completed_mask, completion_result.completion_regions
    )

    # 创建对比图像
    comparison_image = mask_processor.create_comparison_image(
        image, completion_result.original_mask,
        completion_result.completed_mask, completion_result.completion_regions
    )

    # 创建补全前后的灰度图对比
    original_grayscale = mask_processor.create_mask_from_lane_segments(lane_mask, target_pixel_values=True)

    # 手动创建补全后的灰度图（包含补全区域）
    completed_grayscale = original_grayscale.copy()
    # 这里应该将补全区域添加到灰度图中，但由于当前补全算法还未完全实现，暂时跳过

    # 10. 保存结果
    print("10. 保存结果...")
    output_dir = "examples/output_realistic"
    os.makedirs(output_dir, exist_ok=True)

    # 保存图像
    cv2.imwrite(f"{output_dir}/original_image.jpg", image)
    cv2.imwrite(f"{output_dir}/original_grayscale_mask.png", grayscale_mask)
    cv2.imwrite(f"{output_dir}/rgb_detailed_visualization.jpg", rgb_detailed)
    cv2.imwrite(f"{output_dir}/comparison_view.jpg", comparison_image)
    cv2.imwrite(f"{output_dir}/original_reconstructed_mask.png", original_grayscale)

    # 保存各个标线类型的单独mask
    for name, mask in lane_masks.items():
        cv2.imwrite(f"{output_dir}/lane_{name}.png", mask * 255)

    # 保存遮挡物mask
    for name, mask in occlusion_masks.items():
        cv2.imwrite(f"{output_dir}/occlusion_{name}.png", mask * 255)

    # 保存补全区域
    if np.any(completion_result.completion_regions):
        cv2.imwrite(f"{output_dir}/completion_regions.png",
                   completion_result.completion_regions * 255)

    # 生成详细报告
    report = completer.generate_completion_report(completion_result)

    # 添加测试场景信息
    report['test_scenario'] = {
        'description': '真实遮挡场景测试',
        'occlusion_analyses': [
            {
                'lane_id': analysis.lane_id,
                'lane_type': lane_mask.lane_segments[analysis.lane_id].lane_type.value,
                'occlusion_type': analysis.occlusion_type.value,
                'overlap_ratio': analysis.overlap_ratio,
                'confidence': analysis.confidence
            }
            for analysis in occlusion_analyses
        ],
        'inference_results': [
            {
                'rule_id': result.rule_id,
                'lane_id': result.lane_id,
                'lane_type': lane_mask.lane_segments[result.lane_id].lane_type.value,
                'confidence': result.confidence
            }
            for result in inference_results
        ]
    }

    # 保存报告
    import json
    with open(f"{output_dir}/detailed_report.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False, default=str)

    print(f"   结果已保存到: {output_dir}")

    # 11. 显示总结
    print("\n=== 真实遮挡测试总结 ===")
    print(f"场景设置:")
    print(f"  - 标线类型: {len(lane_types)}")
    print(f"  - 遮挡物数量: {len(occlusion_types)}")
    print(f"  - 检测到的遮挡: {len(occlusion_analyses)}")
    print(f"  - 显著遮挡: {len(significant_occlusions)}")

    print(f"推理结果:")
    print(f"  - 匹配规则数: {len(inference_results)}")
    print(f"  - 成功补全: {stats['successful_completions']}")
    print(f"  - 补全像素: {stats['total_completed_pixels']}")

    if len(occlusion_analyses) > 0:
        print(f"遮挡详情:")
        for analysis in occlusion_analyses:
            lane_segment = lane_mask.lane_segments[analysis.lane_id]
            print(f"  - {lane_segment.lane_type.value}: {analysis.overlap_ratio:.1%} 被遮挡")

    print("\n真实遮挡测试完成！")
    return completion_result, report


if __name__ == "__main__":
    try:
        completion_result, report = run_realistic_occlusion_test()
        print("\n测试运行成功！")

    except Exception as e:
        print(f"测试运行出错: {e}")
        import traceback
        traceback.print_exc()
