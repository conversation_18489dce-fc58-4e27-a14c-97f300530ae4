"""
测试邻接检测系统
基于贴合而非重合的遮挡检测逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import cv2
from src.models.mask_model import LaneMask, LaneType, OcclusionType
from src.core.adjacency_detector import AdjacencyDetector
from src.models.advanced_rule_set import create_advanced_rule_set
from src.utils.mask_processor import MaskProcessor


def create_synthetic_adjacency_data():
    """创建合成的邻接数据用于测试"""
    print("创建合成邻接测试数据...")
    
    # 创建一个测试图像
    height, width = 400, 600
    test_mask = np.zeros((height, width), dtype=np.uint8)
    
    # 1. 创建一条水平实线
    cv2.line(test_mask, (50, 200), (550, 200), 3, 5)  # solid_line
    
    # 2. 创建一条虚线
    for x in range(100, 500, 40):
        cv2.line(test_mask, (x, 300), (x + 20, 300), 4, 3)  # dashed_line
    
    # 3. 创建邻接的车辆（贴合但不重合）
    # 车辆在实线上方
    cv2.rectangle(test_mask, (200, 150), (300, 195), 1, -1)  # car
    
    # 4. 创建邻接的杂物
    # 杂物在虚线下方
    cv2.rectangle(test_mask, (250, 305), (320, 340), 11, -1)  # clatter
    
    # 5. 创建一个障碍物
    cv2.circle(test_mask, (450, 180), 25, 12, -1)  # obstacle
    
    # 6. 创建一些箭头
    cv2.rectangle(test_mask, (400, 250), (450, 290), 7, -1)  # arrow_straight
    
    return test_mask


def test_adjacency_detection_synthetic():
    """测试合成数据的邻接检测"""
    print("=== 测试合成数据邻接检测 ===")
    
    # 1. 创建测试数据
    test_mask = create_synthetic_adjacency_data()
    
    # 保存测试数据
    output_dir = "examples/output_adjacency_test"
    os.makedirs(output_dir, exist_ok=True)
    cv2.imwrite(f"{output_dir}/synthetic_test_mask.png", test_mask)
    
    # 2. 解析标线
    processor = MaskProcessor()
    lane_masks, lane_types, _, _ = processor.parse_grayscale_mask(test_mask)
    
    print(f"检测到 {len(lane_masks)} 种标线类型:")
    for name, lane_type in lane_types.items():
        pixels = np.sum(lane_masks[name])
        print(f"  - {name}: {lane_type.value} ({pixels} 像素)")
    
    # 3. 创建标线mask对象
    lane_mask = LaneMask(test_mask.shape)
    for name, mask in lane_masks.items():
        lane_type = lane_types[name]
        lane_id = lane_mask.add_lane_segment(lane_type, mask)
        print(f"  添加标线段: {name} (ID: {lane_id})")
    
    # 4. 检测遮挡物
    detector = AdjacencyDetector({
        'max_gap_distance': 15,
        'min_adjacency_length': 10,
        'confidence_threshold': 0.2
    })
    
    occlusion_masks, occlusion_types = detector.detect_occlusions_from_mask(test_mask)
    
    print(f"检测到 {len(occlusion_masks)} 种遮挡物:")
    for name, mask in occlusion_masks.items():
        pixels = np.sum(mask)
        print(f"  - {name}: {occlusion_types[name].value} ({pixels} 像素)")
    
    # 5. 检测邻接关系
    adjacency_analyses = detector.detect_adjacencies(lane_mask, occlusion_masks, occlusion_types)
    
    print(f"检测到 {len(adjacency_analyses)} 个邻接关系:")
    for analysis in adjacency_analyses:
        lane_segment = lane_mask.lane_segments[analysis.lane_id]
        print(f"  - 标线 {analysis.lane_id} ({lane_segment.lane_type.value}) "
              f"与遮挡物 {analysis.occlusion_id} 邻接")
        print(f"    邻接比例: {analysis.adjacency_ratio:.2%}")
        print(f"    邻接长度: {analysis.adjacency_length:.1f}")
        print(f"    间隙距离: {analysis.gap_distance:.1f}")
        print(f"    置信度: {analysis.confidence:.3f}")
    
    # 6. 过滤显著邻接
    significant_adjacencies = detector.filter_significant_adjacencies(adjacency_analyses)
    print(f"其中 {len(significant_adjacencies)} 个为显著邻接")
    
    # 7. 创建可视化
    create_adjacency_visualization(
        test_mask, lane_mask, occlusion_masks, significant_adjacencies, output_dir
    )
    
    return significant_adjacencies


def test_adjacency_detection_real_data():
    """测试真实数据的邻接检测"""
    print("\n=== 测试真实数据邻接检测 ===")
    
    # 1. 加载真实数据
    image_path = "data/images/DJI_20220915153843_0320.JPG"
    mask_path = "data/masks/DJI_20220915153843_0320.png"
    
    try:
        image = cv2.imread(image_path)
        mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
        
        if image is None or mask is None:
            print("无法加载真实数据文件")
            return []
        
        # 缩放数据
        scale_factor = 0.25
        image = cv2.resize(image, None, fx=scale_factor, fy=scale_factor)
        mask = cv2.resize(mask, None, fx=scale_factor, fy=scale_factor, interpolation=cv2.INTER_NEAREST)
        
        print(f"缩放后图像尺寸: {image.shape}")
        print(f"Mask像素值: {np.unique(mask)}")
        
    except Exception as e:
        print(f"加载真实数据时出错: {e}")
        return []
    
    # 2. 解析标线
    processor = MaskProcessor()
    lane_masks, lane_types, _, _ = processor.parse_grayscale_mask(mask)
    
    print(f"检测到 {len(lane_masks)} 种标线类型:")
    for name, lane_type in lane_types.items():
        pixels = np.sum(lane_masks[name])
        print(f"  - {name}: {lane_type.value} ({pixels} 像素)")
    
    # 3. 创建标线mask对象
    lane_mask = LaneMask(image.shape[:2])
    for name, lane_mask_data in lane_masks.items():
        lane_type = lane_types[name]
        lane_id = lane_mask.add_lane_segment(lane_type, lane_mask_data)
    
    # 4. 检测遮挡物
    detector = AdjacencyDetector({
        'max_gap_distance': 20,
        'min_adjacency_length': 15,
        'confidence_threshold': 0.1
    })
    
    occlusion_masks, occlusion_types = detector.detect_occlusions_from_mask(mask)
    
    print(f"检测到 {len(occlusion_masks)} 种遮挡物:")
    for name, occlusion_mask in occlusion_masks.items():
        pixels = np.sum(occlusion_mask)
        print(f"  - {name}: {occlusion_types[name].value} ({pixels} 像素)")
    
    if len(occlusion_masks) == 0:
        print("真实数据中没有检测到遮挡物")
        return []
    
    # 5. 检测邻接关系
    adjacency_analyses = detector.detect_adjacencies(lane_mask, occlusion_masks, occlusion_types)
    
    print(f"检测到 {len(adjacency_analyses)} 个邻接关系:")
    for analysis in adjacency_analyses:
        lane_segment = lane_mask.lane_segments[analysis.lane_id]
        print(f"  - 标线 {analysis.lane_id} ({lane_segment.lane_type.value}) "
              f"与遮挡物 {analysis.occlusion_id} 邻接")
        print(f"    邻接比例: {analysis.adjacency_ratio:.2%}")
        print(f"    邻接长度: {analysis.adjacency_length:.1f}")
        print(f"    间隙距离: {analysis.gap_distance:.1f}")
        print(f"    置信度: {analysis.confidence:.3f}")
    
    # 6. 过滤显著邻接
    significant_adjacencies = detector.filter_significant_adjacencies(adjacency_analyses)
    print(f"其中 {len(significant_adjacencies)} 个为显著邻接")
    
    # 7. 创建可视化
    output_dir = "examples/output_adjacency_test"
    create_adjacency_visualization(
        mask, lane_mask, occlusion_masks, significant_adjacencies, output_dir, "real_data"
    )
    
    return significant_adjacencies


def create_adjacency_visualization(mask, lane_mask, occlusion_masks, adjacencies, output_dir, prefix="synthetic"):
    """创建邻接关系可视化"""
    print("创建邻接关系可视化...")
    
    h, w = mask.shape
    
    # 创建RGB可视化
    rgb_vis = np.zeros((h, w, 3), dtype=np.uint8)
    
    # 1. 绘制标线（白色）
    for lane_segment in lane_mask.lane_segments.values():
        rgb_vis[lane_segment.mask > 0] = [255, 255, 255]
    
    # 2. 绘制遮挡物（不同颜色）
    colors = {
        'car': [255, 0, 0],      # 红色
        'human': [0, 255, 0],    # 绿色
        'clatter': [0, 0, 255],  # 蓝色
        'obstacle': [255, 255, 0] # 黄色
    }
    
    for name, occlusion_mask in occlusion_masks.items():
        color = colors.get(name, [128, 128, 128])
        rgb_vis[occlusion_mask > 0] = color
    
    # 3. 标记邻接点（洋红色）
    for adjacency in adjacencies:
        for point in adjacency.contact_points:
            x, y = point
            if 0 <= x < w and 0 <= y < h:
                cv2.circle(rgb_vis, (x, y), 2, (255, 0, 255), -1)
    
    # 4. 保存可视化
    cv2.imwrite(f"{output_dir}/{prefix}_adjacency_visualization.jpg", rgb_vis)
    
    # 5. 创建详细分析图
    if len(adjacencies) > 0:
        analysis_img = create_detailed_analysis_image(mask, lane_mask, adjacencies)
        cv2.imwrite(f"{output_dir}/{prefix}_detailed_analysis.jpg", analysis_img)
    
    print(f"可视化结果已保存到: {output_dir}")


def create_detailed_analysis_image(mask, lane_mask, adjacencies):
    """创建详细分析图像"""
    h, w = mask.shape
    
    # 创建2x2布局
    result = np.zeros((h * 2, w * 2, 3), dtype=np.uint8)
    
    # 左上：原始mask
    original_rgb = cv2.applyColorMap(mask * 10, cv2.COLORMAP_JET)
    result[:h, :w] = original_rgb
    
    # 右上：标线mask
    lane_rgb = np.zeros((h, w, 3), dtype=np.uint8)
    for lane_segment in lane_mask.lane_segments.values():
        lane_rgb[lane_segment.mask > 0] = [255, 255, 255]
    result[:h, w:] = lane_rgb
    
    # 左下：遮挡物mask
    occlusion_rgb = np.zeros((h, w, 3), dtype=np.uint8)
    for occlusion_region in lane_mask.occlusion_regions.values():
        occlusion_rgb[occlusion_region.mask > 0] = [255, 0, 0]
    result[h:, :w] = occlusion_rgb
    
    # 右下：邻接分析
    adjacency_rgb = np.zeros((h, w, 3), dtype=np.uint8)
    for adjacency in adjacencies:
        lane_segment = lane_mask.lane_segments[adjacency.lane_id]
        occlusion_region = lane_mask.occlusion_regions[adjacency.occlusion_id]
        
        # 标线（绿色）
        adjacency_rgb[lane_segment.mask > 0] = [0, 255, 0]
        # 遮挡物（红色）
        adjacency_rgb[occlusion_region.mask > 0] = [255, 0, 0]
        # 接触点（白色）
        for point in adjacency.contact_points:
            x, y = point
            if 0 <= x < w and 0 <= y < h:
                cv2.circle(adjacency_rgb, (x, y), 1, (255, 255, 255), -1)
    
    result[h:, w:] = adjacency_rgb
    
    # 添加标题
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(result, "Original Mask", (10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Lane Masks", (w + 10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Occlusion Masks", (10, h + 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Adjacency Analysis", (w + 10, h + 25), font, 0.7, (255, 255, 255), 2)
    
    return result


def main():
    """主函数"""
    print("开始测试邻接检测系统...")
    
    try:
        # 测试合成数据
        synthetic_adjacencies = test_adjacency_detection_synthetic()
        
        # 测试真实数据
        real_adjacencies = test_adjacency_detection_real_data()
        
        print("\n=== 测试总结 ===")
        print(f"合成数据邻接数: {len(synthetic_adjacencies)}")
        print(f"真实数据邻接数: {len(real_adjacencies)}")
        print("结果已保存到 examples/output_adjacency_test/")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
