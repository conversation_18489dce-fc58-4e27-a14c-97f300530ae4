"""
测试基于边缘推理的补全系统
基于用户专业理解的完整实现
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import cv2
import json
from src.core.lane_entity_analyzer import LaneEntityAnalyzer
from src.core.edge_based_completer import EdgeBasedCompleter
from src.utils.mask_processor import MaskProcessor


def test_edge_based_completion_system():
    """测试基于边缘推理的补全系统"""
    print("=== 基于边缘推理的补全系统测试 ===")

    # 1. 加载真实数据
    print("1. 加载真实数据...")
    image_path = "data/images/DJI_20220915153843_0320.JPG"
    mask_path = "data/masks/DJI_20220915153843_0320.png"

    try:
        image = cv2.imread(image_path)
        mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)

        if image is None or mask is None:
            print("无法加载数据文件")
            return None

        # 缩放数据
        scale_factor = 0.25
        image = cv2.resize(image, None, fx=scale_factor, fy=scale_factor)
        mask = cv2.resize(mask, None, fx=scale_factor, fy=scale_factor, interpolation=cv2.INTER_NEAREST)

        print(f"   缩放后图像尺寸: {image.shape}")

    except Exception as e:
        print(f"   加载数据时出错: {e}")
        return None

    # 2. 解析标线数据
    print("2. 解析标线数据...")
    processor = MaskProcessor()
    lane_masks, lane_types, _, _ = processor.parse_grayscale_mask(mask)

    print(f"   检测到 {len(lane_masks)} 种标线类型:")
    for name, lane_type in lane_types.items():
        pixels = np.sum(lane_masks[name])
        print(f"   - {name}: {lane_type.value} ({pixels} 像素)")

    # 3. 创建统一的遮挡物mask
    print("3. 创建统一遮挡物mask...")
    # 遮挡物像素值：1(car), 2(human), 11(clatter), 12(obstacle)
    occlusion_pixels = [1, 2, 11, 12]
    unified_occlusion_mask = np.zeros_like(mask)

    for pixel_value in occlusion_pixels:
        unified_occlusion_mask[mask == pixel_value] = 1

    occlusion_pixel_count = np.sum(unified_occlusion_mask)
    print(f"   统一遮挡物: {occlusion_pixel_count} 像素")

    if occlusion_pixel_count == 0:
        print("   没有检测到遮挡物")
        return None

    # 4. 分析标线实体和遮挡断裂
    print("4. 分析标线实体和遮挡断裂...")
    analyzer = LaneEntityAnalyzer({
        'edge_detection_kernel': 3,
        'skeleton_smoothing': True,
        'curvature_window': 5,
        'width_sampling_interval': 10
    })

    lane_entities, occlusion_breaks = analyzer.analyze_lane_entities(
        lane_masks, lane_types, unified_occlusion_mask
    )

    print(f"   分析结果:")
    print(f"   - 标线实体: {len(lane_entities)}")
    print(f"   - 遮挡断裂: {len(occlusion_breaks)}")

    if len(occlusion_breaks) == 0:
        print("   没有检测到遮挡断裂")
        return None

    # 5. 执行边缘推理补全
    print("5. 执行边缘推理补全...")
    completer = EdgeBasedCompleter({
        'interpolation_method': 'cubic',
        'smoothing_factor': 0.1,
        'width_transition_smoothness': 0.2,
        'min_inference_confidence': 0.3
    })

    completion_results = completer.complete_occlusion_breaks(
        lane_entities, occlusion_breaks, image.shape[:2]
    )

    print(f"   补全结果:")
    print(f"   - 成功补全: {len(completion_results)}")

    total_completed_pixels = 0
    for i, result in enumerate(completion_results):
        total_completed_pixels += result.completed_pixels
        print(f"   - 补全 {i+1}: {result.lane_type.value}, "
              f"{result.completed_pixels} 像素, "
              f"方法: {result.completion_method}, "
              f"置信度: {result.confidence:.3f}")

    # 6. 生成可视化
    print("6. 生成可视化...")
    output_dir = "examples/output_edge_based_completion"
    os.makedirs(output_dir, exist_ok=True)

    # 保存原始数据
    cv2.imwrite(f"{output_dir}/original_image.jpg", image)
    cv2.imwrite(f"{output_dir}/original_mask.png", mask)
    cv2.imwrite(f"{output_dir}/unified_occlusion_mask.png", unified_occlusion_mask * 255)

    # 保存标线实体分析结果
    for i, entity in enumerate(lane_entities):
        entity_vis = create_entity_visualization(entity, image.shape[:2])
        cv2.imwrite(f"{output_dir}/entity_{i}_{entity.lane_type.value}.png", entity_vis)

    # 保存遮挡断裂分析结果
    for i, break_obj in enumerate(occlusion_breaks):
        break_vis = create_break_visualization(break_obj, image.shape[:2])
        cv2.imwrite(f"{output_dir}/break_{i}.png", break_vis)

    # 保存补全结果
    for i, result in enumerate(completion_results):
        cv2.imwrite(f"{output_dir}/completion_{i}_{result.completion_method}.png",
                   result.completed_mask * 255)

        # 保存推理路径
        if result.inference_result.center_path:
            inference_vis = create_inference_visualization(result.inference_result, image.shape[:2])
            cv2.imwrite(f"{output_dir}/inference_{i}.png", inference_vis)

    # 创建综合可视化
    comprehensive_vis = create_edge_based_comprehensive_visualization(
        image, mask, lane_entities, occlusion_breaks, completion_results
    )
    cv2.imwrite(f"{output_dir}/edge_based_comprehensive.jpg", comprehensive_vis)

    # 创建前后对比
    before_after = create_edge_based_before_after_comparison(
        image, mask, completion_results
    )
    cv2.imwrite(f"{output_dir}/edge_based_before_after.jpg", before_after)

    # 7. 生成详细报告
    print("7. 生成详细报告...")
    report = {
        'system_info': {
            'method': 'edge_based_inference',
            'approach': 'dual_edge_reasoning',
            'interpolation': 'bezier_curve'
        },
        'analysis_results': {
            'lane_entities': len(lane_entities),
            'occlusion_breaks': len(occlusion_breaks),
            'successful_completions': len(completion_results),
            'total_completed_pixels': total_completed_pixels
        },
        'entity_analysis': [
            {
                'entity_id': i,
                'lane_type': entity.lane_type.value,
                'center_line_length': len(entity.center_line),
                'average_width': float(np.mean(entity.width_profile)) if entity.width_profile else 0,
                'has_special_attributes': bool(entity.dash_segments or entity.parallel_lines or entity.fill_pattern)
            }
            for i, entity in enumerate(lane_entities)
        ],
        'break_analysis': [
            {
                'break_id': i,
                'entity_id': break_obj.entity_id,
                'break_start': break_obj.break_start,
                'break_end': break_obj.break_end,
                'start_width': break_obj.start_width,
                'end_width': break_obj.end_width,
                'occlusion_region_size': int(np.sum(break_obj.occlusion_region))
            }
            for i, break_obj in enumerate(occlusion_breaks)
        ],
        'completion_results': [
            {
                'completion_id': i,
                'lane_type': result.lane_type.value,
                'method': result.completion_method,
                'completed_pixels': result.completed_pixels,
                'confidence': result.confidence,
                'inference_confidence': result.inference_result.confidence,
                'center_path_length': len(result.inference_result.center_path)
            }
            for i, result in enumerate(completion_results)
        ],
        'performance_metrics': {
            'completion_success_rate': len(completion_results) / max(len(occlusion_breaks), 1),
            'average_completion_pixels': total_completed_pixels / max(len(completion_results), 1),
            'total_improvement_pixels': total_completed_pixels,
            'average_inference_confidence': np.mean([r.inference_result.confidence for r in completion_results]) if completion_results else 0
        }
    }

    with open(f"{output_dir}/edge_based_completion_report.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False, default=str)

    # 8. 显示总结
    print("\n=== 基于边缘推理的补全系统测试总结 ===")
    print(f"🎯 分析结果:")
    print(f"  - 标线实体: {len(lane_entities)}")
    print(f"  - 遮挡断裂: {len(occlusion_breaks)}")
    print(f"  - 成功补全: {len(completion_results)}")

    print(f"📊 补全效果:")
    print(f"  - 总补全像素: {total_completed_pixels:,}")
    print(f"  - 平均每次补全: {total_completed_pixels // max(len(completion_results), 1):,} 像素")
    print(f"  - 补全成功率: {len(completion_results) / max(len(occlusion_breaks), 1) * 100:.1f}%")

    if completion_results:
        avg_confidence = np.mean([r.inference_result.confidence for r in completion_results])
        print(f"  - 平均推理置信度: {avg_confidence:.3f}")

    print(f"✅ 补全详情:")
    for i, result in enumerate(completion_results):
        print(f"  {i+1}. {result.lane_type.value}: {result.completed_pixels:,} 像素 "
              f"({result.completion_method}, 推理置信度: {result.inference_result.confidence:.3f})")

    print(f"\n🎉 基于边缘推理的补全系统测试完成！")
    print(f"📁 结果已保存到: {output_dir}")

    return report


def create_entity_visualization(entity, image_shape):
    """创建标线实体可视化"""
    vis = np.zeros((*image_shape, 3), dtype=np.uint8)

    # 绘制中心线（红色）
    for i in range(len(entity.center_line) - 1):
        p1 = entity.center_line[i]
        p2 = entity.center_line[i + 1]
        cv2.line(vis, p1, p2, (0, 0, 255), 2)

    # 绘制边缘（绿色和蓝色）
    if entity.left_edge.points:
        for i in range(len(entity.left_edge.points) - 1):
            p1 = entity.left_edge.points[i]
            p2 = entity.left_edge.points[i + 1]
            cv2.line(vis, p1, p2, (0, 255, 0), 1)

    if entity.right_edge.points:
        for i in range(len(entity.right_edge.points) - 1):
            p1 = entity.right_edge.points[i]
            p2 = entity.right_edge.points[i + 1]
            cv2.line(vis, p1, p2, (255, 0, 0), 1)

    return vis


def create_break_visualization(break_obj, image_shape):
    """创建断裂可视化"""
    vis = np.zeros((*image_shape, 3), dtype=np.uint8)

    # 绘制断裂点（黄色圆圈）
    cv2.circle(vis, break_obj.break_start, 5, (0, 255, 255), -1)
    cv2.circle(vis, break_obj.break_end, 5, (0, 255, 255), -1)

    # 绘制边缘点（红色和蓝色）
    cv2.circle(vis, break_obj.start_edge_left, 3, (0, 0, 255), -1)
    cv2.circle(vis, break_obj.start_edge_right, 3, (255, 0, 0), -1)
    cv2.circle(vis, break_obj.end_edge_left, 3, (0, 0, 255), -1)
    cv2.circle(vis, break_obj.end_edge_right, 3, (255, 0, 0), -1)

    return vis


def create_inference_visualization(inference_result, image_shape):
    """创建推理可视化"""
    vis = np.zeros((*image_shape, 3), dtype=np.uint8)

    # 绘制推理的中心路径（黄色）
    for i in range(len(inference_result.center_path) - 1):
        p1 = inference_result.center_path[i]
        p2 = inference_result.center_path[i + 1]
        cv2.line(vis, p1, p2, (0, 255, 255), 2)

    # 绘制推理的边缘路径（绿色和蓝色）
    for i in range(len(inference_result.left_edge_path) - 1):
        p1 = inference_result.left_edge_path[i]
        p2 = inference_result.left_edge_path[i + 1]
        cv2.line(vis, p1, p2, (0, 255, 0), 1)

    for i in range(len(inference_result.right_edge_path) - 1):
        p1 = inference_result.right_edge_path[i]
        p2 = inference_result.right_edge_path[i + 1]
        cv2.line(vis, p1, p2, (255, 0, 0), 1)

    return vis


def create_edge_based_comprehensive_visualization(image, mask, entities, breaks, completion_results):
    """创建边缘推理综合可视化"""
    h, w = image.shape[:2]

    # 创建2x3布局
    result = np.zeros((h * 2, w * 3, 3), dtype=np.uint8)

    # 第一行：原图、标线实体、遮挡断裂
    result[:h, :w] = image

    # 标线实体可视化
    entity_vis = np.zeros((h, w, 3), dtype=np.uint8)
    for entity in entities:
        # 绘制中心线
        for i in range(len(entity.center_line) - 1):
            p1 = entity.center_line[i]
            p2 = entity.center_line[i + 1]
            cv2.line(entity_vis, p1, p2, (255, 255, 255), 2)
    result[:h, w:w*2] = entity_vis

    # 遮挡断裂可视化
    break_vis = entity_vis.copy()
    for break_obj in breaks:
        cv2.circle(break_vis, break_obj.break_start, 5, (0, 255, 255), -1)
        cv2.circle(break_vis, break_obj.break_end, 5, (0, 255, 255), -1)
    result[:h, w*2:] = break_vis

    # 第二行：推理路径、补全结果、最终效果
    # 推理路径
    inference_vis = np.zeros((h, w, 3), dtype=np.uint8)
    for comp_result in completion_results:
        inference = comp_result.inference_result
        for i in range(len(inference.center_path) - 1):
            p1 = inference.center_path[i]
            p2 = inference.center_path[i + 1]
            cv2.line(inference_vis, p1, p2, (0, 255, 255), 2)
    result[h:, :w] = inference_vis

    # 补全结果
    completion_vis = np.zeros((h, w, 3), dtype=np.uint8)
    for i, comp_result in enumerate(completion_results):
        color = [(i * 60) % 255, (i * 120) % 255, (i * 180) % 255]
        completion_vis[comp_result.completed_mask > 0] = color
    result[h:, w:w*2] = completion_vis

    # 最终效果
    final_vis = entity_vis.copy()
    for comp_result in completion_results:
        final_vis[comp_result.completed_mask > 0] = [0, 255, 0]  # 绿色补全
    result[h:, w*2:] = final_vis

    # 添加标题
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(result, "Original Image", (10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Lane Entities", (w + 10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Occlusion Breaks", (w*2 + 10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Edge Inference", (10, h + 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Completions", (w + 10, h + 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Final Result", (w*2 + 10, h + 25), font, 0.7, (255, 255, 255), 2)

    return result


def create_edge_based_before_after_comparison(image, original_mask, completion_results):
    """创建边缘推理前后对比"""
    h, w = image.shape[:2]
    result = np.zeros((h, w * 2, 3), dtype=np.uint8)

    # 左侧：原始
    left_vis = image.copy()
    overlay_left = np.zeros_like(image)
    overlay_left[original_mask > 0] = [255, 255, 255]

    # 右侧：补全后
    right_vis = image.copy()
    overlay_right = np.zeros_like(image)
    overlay_right[original_mask > 0] = [255, 255, 255]

    # 添加补全结果（绿色）
    for comp_result in completion_results:
        overlay_right[comp_result.completed_mask > 0] = [0, 255, 0]

    # 混合图像
    alpha = 0.6
    left_vis = cv2.addWeighted(left_vis, 1-alpha, overlay_left, alpha, 0)
    right_vis = cv2.addWeighted(right_vis, 1-alpha, overlay_right, alpha, 0)

    result[:, :w] = left_vis
    result[:, w:] = right_vis

    # 添加标题和统计信息
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(result, "Before Edge-Based Completion", (10, 30), font, 1.0, (255, 255, 255), 2)
    cv2.putText(result, "After Edge-Based Completion", (w + 10, 30), font, 1.0, (255, 255, 255), 2)

    # 添加统计信息
    total_completed = sum(np.sum(comp.completed_mask) for comp in completion_results)
    cv2.putText(result, f"Original Pixels: {np.sum(original_mask > 0):,}",
                (10, h - 60), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, f"Added Pixels: {total_completed:,}",
                (w + 10, h - 60), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, f"Edge Inferences: {len(completion_results)}",
                (w + 10, h - 30), font, 0.7, (255, 255, 255), 2)

    return result


if __name__ == "__main__":
    try:
        report = test_edge_based_completion_system()
        if report is not None:
            print("\n🎉 基于边缘推理的补全系统测试运行成功！")

    except Exception as e:
        print(f"边缘推理补全系统测试运行出错: {e}")
        import traceback
        traceback.print_exc()
