"""
Mask处理工具
处理灰度图mask，提取标线信息，过滤非标线类别
"""

import numpy as np
import cv2
from typing import Dict, List, Tuple, Optional
import logging

from ..models.mask_model import (
    LaneType, LaneMask, OcclusionType,
    PIXEL_TO_LANE_TYPE, NON_LANE_PIXELS, LANE_TYPE_COLORS, LANE_TYPE_TO_PIXEL
)


class MaskProcessor:
    """Mask处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def parse_grayscale_mask(self, mask: np.ndarray) -> Tuple[Dict[str, np.ndarray], Dict[str, LaneType], Dict[str, np.ndarray], Dict[str, OcclusionType]]:
        """
        解析灰度mask，分离标线和遮挡物
        
        Args:
            mask: 灰度mask图像
            
        Returns:
            (lane_masks, lane_types, occlusion_masks, occlusion_types)
        """
        lane_masks = {}
        lane_types = {}
        occlusion_masks = {}
        occlusion_types = {}
        
        # 获取所有唯一像素值
        unique_pixels = np.unique(mask)
        self.logger.info(f"Found unique pixel values: {unique_pixels}")
        
        for pixel_value in unique_pixels:
            if pixel_value == 0:  # 背景
                continue
            
            # 创建当前像素值的mask
            current_mask = (mask == pixel_value).astype(np.uint8)
            
            if pixel_value in NON_LANE_PIXELS:
                # 非标线类别（车辆、行人）作为遮挡物
                if pixel_value == 1:  # car
                    occlusion_masks['car'] = current_mask
                    occlusion_types['car'] = OcclusionType.DYNAMIC
                elif pixel_value == 2:  # human
                    occlusion_masks['human'] = current_mask
                    occlusion_types['human'] = OcclusionType.DYNAMIC
            
            elif pixel_value in PIXEL_TO_LANE_TYPE:
                # 标线类别
                lane_type = PIXEL_TO_LANE_TYPE[pixel_value]
                lane_name = f"{lane_type.value}_{pixel_value}"
                
                lane_masks[lane_name] = current_mask
                lane_types[lane_name] = lane_type
                
                self.logger.info(f"Found lane type: {lane_type.value} (pixel={pixel_value}, pixels={np.sum(current_mask)})")
            
            else:
                # 未知像素值，记录警告
                self.logger.warning(f"Unknown pixel value: {pixel_value} (pixels={np.sum(current_mask)})")
        
        return lane_masks, lane_types, occlusion_masks, occlusion_types
    
    def create_rgb_visualization(self, 
                               original_mask: np.ndarray,
                               completed_mask: np.ndarray,
                               completion_regions: np.ndarray) -> np.ndarray:
        """
        创建RGB可视化图像
        
        Args:
            original_mask: 原始标线mask
            completed_mask: 补全后的mask
            completion_regions: 补全区域mask
            
        Returns:
            RGB可视化图像
        """
        height, width = original_mask.shape
        rgb_image = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 背景设为深灰色
        rgb_image[:, :] = (50, 50, 50)
        
        # 显示原始标线（白色）
        rgb_image[original_mask > 0] = (255, 255, 255)
        
        # 显示补全区域（红色）
        rgb_image[completion_regions > 0] = (255, 0, 0)
        
        return rgb_image
    
    def create_detailed_rgb_visualization(self, 
                                        lane_mask_obj: LaneMask,
                                        completed_mask: np.ndarray,
                                        completion_regions: np.ndarray) -> np.ndarray:
        """
        创建详细的RGB可视化图像，按标线类型着色
        
        Args:
            lane_mask_obj: 标线mask对象
            completed_mask: 补全后的mask
            completion_regions: 补全区域mask
            
        Returns:
            RGB可视化图像
        """
        height, width = lane_mask_obj.height, lane_mask_obj.width
        rgb_image = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 背景设为深灰色
        rgb_image[:, :] = (30, 30, 30)
        
        # 按类型为每个标线段着色
        for lane_id, lane_segment in lane_mask_obj.lane_segments.items():
            lane_type = lane_segment.lane_type
            color = LANE_TYPE_COLORS.get(lane_type, (255, 255, 255))
            
            # BGR格式
            color_bgr = (color[2], color[1], color[0])
            rgb_image[lane_segment.mask > 0] = color_bgr
        
        # 显示遮挡区域（半透明红色）
        for occlusion_id, occlusion_region in lane_mask_obj.occlusion_regions.items():
            mask_area = occlusion_region.mask > 0
            rgb_image[mask_area] = rgb_image[mask_area] * 0.5 + np.array([0, 0, 128]) * 0.5
        
        # 高亮显示补全区域（亮绿色边框）
        if np.any(completion_regions):
            # 创建边框
            kernel = np.ones((3, 3), np.uint8)
            completion_dilated = cv2.dilate(completion_regions, kernel, iterations=1)
            completion_border = completion_dilated - completion_regions
            
            rgb_image[completion_border > 0] = (0, 255, 0)  # 绿色边框
            rgb_image[completion_regions > 0] = (0, 200, 0)  # 绿色填充
        
        return rgb_image
    
    def create_mask_from_lane_segments(self, 
                                     lane_mask_obj: LaneMask,
                                     target_pixel_values: bool = True) -> np.ndarray:
        """
        从标线段创建灰度mask
        
        Args:
            lane_mask_obj: 标线mask对象
            target_pixel_values: 是否使用目标像素值，否则使用二值mask
            
        Returns:
            灰度mask
        """
        height, width = lane_mask_obj.height, lane_mask_obj.width
        result_mask = np.zeros((height, width), dtype=np.uint8)
        
        for lane_id, lane_segment in lane_mask_obj.lane_segments.items():
            lane_type = lane_segment.lane_type
            
            if target_pixel_values and lane_type in LANE_TYPE_TO_PIXEL:
                pixel_value = LANE_TYPE_TO_PIXEL[lane_type]
            else:
                pixel_value = 255  # 二值mask
            
            result_mask[lane_segment.mask > 0] = pixel_value
        
        return result_mask
    
    def filter_small_regions(self, mask: np.ndarray, min_area: int = 10) -> np.ndarray:
        """
        过滤小区域
        
        Args:
            mask: 输入mask
            min_area: 最小区域面积
            
        Returns:
            过滤后的mask
        """
        # 查找连通组件
        num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(mask, connectivity=8)
        
        # 创建过滤后的mask
        filtered_mask = np.zeros_like(mask)
        
        for i in range(1, num_labels):  # 跳过背景（标签0）
            area = stats[i, cv2.CC_STAT_AREA]
            if area >= min_area:
                filtered_mask[labels == i] = mask[labels == i]
        
        return filtered_mask
    
    def smooth_mask(self, mask: np.ndarray, kernel_size: int = 3) -> np.ndarray:
        """
        平滑mask
        
        Args:
            mask: 输入mask
            kernel_size: 核大小
            
        Returns:
            平滑后的mask
        """
        kernel = np.ones((kernel_size, kernel_size), np.uint8)
        
        # 形态学操作
        smoothed = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        smoothed = cv2.morphologyEx(smoothed, cv2.MORPH_OPEN, kernel)
        
        return smoothed
    
    def create_comparison_image(self, 
                              original_image: np.ndarray,
                              original_mask: np.ndarray,
                              completed_mask: np.ndarray,
                              completion_regions: np.ndarray) -> np.ndarray:
        """
        创建对比图像
        
        Args:
            original_image: 原始图像
            original_mask: 原始mask
            completed_mask: 补全mask
            completion_regions: 补全区域
            
        Returns:
            对比图像
        """
        h, w = original_image.shape[:2]
        
        # 创建2x2布局
        result = np.zeros((h * 2, w * 2, 3), dtype=np.uint8)
        
        # 左上：原始图像
        if len(original_image.shape) == 3:
            result[:h, :w] = original_image
        else:
            result[:h, :w] = cv2.cvtColor(original_image, cv2.COLOR_GRAY2BGR)
        
        # 右上：原始mask可视化
        original_rgb = self.create_rgb_visualization(original_mask, original_mask, np.zeros_like(original_mask))
        result[:h, w:] = original_rgb
        
        # 左下：补全结果
        completed_rgb = self.create_rgb_visualization(original_mask, completed_mask, completion_regions)
        result[h:, :w] = completed_rgb
        
        # 右下：补全区域高亮
        highlight_rgb = np.zeros((h, w, 3), dtype=np.uint8)
        highlight_rgb[:, :] = (30, 30, 30)  # 深灰背景
        highlight_rgb[original_mask > 0] = (100, 100, 100)  # 原始标线为灰色
        highlight_rgb[completion_regions > 0] = (0, 255, 0)  # 补全区域为绿色
        result[h:, w:] = highlight_rgb
        
        # 添加标题
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(result, "Original Image", (10, 25), font, 0.7, (255, 255, 255), 2)
        cv2.putText(result, "Original Mask", (w + 10, 25), font, 0.7, (255, 255, 255), 2)
        cv2.putText(result, "Completed Result", (10, h + 25), font, 0.7, (255, 255, 255), 2)
        cv2.putText(result, "Completion Regions", (w + 10, h + 25), font, 0.7, (255, 255, 255), 2)
        
        return result
