"""
真实数据简化测试
先缩放图像以便快速测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import cv2
from src.models.mask_model import LaneMask, LaneType, OcclusionType
from src.core.occlusion_detector import OcclusionDetector
from src.core.rule_engine import RuleEngine
from src.core.structure_completer import StructureCompleter
from src.utils.mask_processor import MaskProcessor


def run_real_data_simple_test():
    """运行真实数据简化测试"""
    print("=== 真实数据简化测试 ===")
    
    # 1. 加载并缩放真实数据
    print("1. 加载并缩放真实数据...")
    image_path = "data/images/DJI_20220915153843_0320.JPG"
    mask_path = "data/masks/DJI_20220915153843_0320.png"
    
    try:
        image = cv2.imread(image_path)
        original_mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
        
        if image is None or original_mask is None:
            print(f"   错误：无法加载数据文件")
            return None, None
        
        print(f"   原始图像尺寸: {image.shape}")
        print(f"   原始Mask尺寸: {original_mask.shape}")
        
        # 缩放到1/4大小以便快速处理
        scale_factor = 0.25
        image = cv2.resize(image, None, fx=scale_factor, fy=scale_factor)
        original_mask = cv2.resize(original_mask, None, fx=scale_factor, fy=scale_factor, interpolation=cv2.INTER_NEAREST)
        
        print(f"   缩放后图像尺寸: {image.shape}")
        print(f"   缩放后Mask尺寸: {original_mask.shape}")
        print(f"   Mask像素值: {np.unique(original_mask)}")
        
    except Exception as e:
        print(f"   加载数据时出错: {e}")
        return None, None
    
    # 2. 创建简单的合成遮挡
    print("2. 创建合成遮挡...")
    
    # 找到有标线的区域
    lane_pixels = np.where(original_mask > 0)
    if len(lane_pixels[0]) == 0:
        print("   没有找到标线像素")
        return None, None
    
    # 创建一个车辆遮挡
    center_idx = len(lane_pixels[0]) // 2
    center_y = lane_pixels[0][center_idx]
    center_x = lane_pixels[1][center_idx]
    
    # 创建遮挡区域
    occlusion_masks = {}
    occlusion_types = {}
    
    # 车辆遮挡（矩形）
    car_mask = np.zeros_like(original_mask)
    car_width, car_height = 60, 40
    y1 = max(0, center_y - car_height // 2)
    y2 = min(original_mask.shape[0], center_y + car_height // 2)
    x1 = max(0, center_x - car_width // 2)
    x2 = min(original_mask.shape[1], center_x + car_width // 2)
    car_mask[y1:y2, x1:x2] = 1
    
    occlusion_masks['car'] = car_mask
    occlusion_types['car'] = OcclusionType.DYNAMIC
    
    print(f"   创建车辆遮挡: {np.sum(car_mask)} 像素")
    
    # 3. 初始化处理器
    print("3. 初始化处理器...")
    mask_processor = MaskProcessor()
    detector = OcclusionDetector({'confidence_threshold': 0.1, 'overlap_threshold': 0.1})
    engine = RuleEngine()
    completer = StructureCompleter()
    
    # 4. 解析标线数据
    print("4. 解析标线数据...")
    lane_masks, lane_types, _, _ = mask_processor.parse_grayscale_mask(original_mask)
    
    print(f"   检测到 {len(lane_masks)} 种标线类型:")
    for name, lane_type in lane_types.items():
        pixels = np.sum(lane_masks[name])
        print(f"   - {name}: {lane_type.value} ({pixels} 像素)")
    
    # 5. 创建标线mask对象
    print("5. 创建标线mask对象...")
    lane_mask = LaneMask(image.shape[:2])
    
    for name, mask in lane_masks.items():
        lane_type = lane_types[name]
        lane_id = lane_mask.add_lane_segment(lane_type, mask)
        print(f"   添加标线段: {name} (ID: {lane_id})")
    
    # 6. 检测遮挡
    print("6. 检测遮挡...")
    occlusion_analyses = detector.detect_occlusions(
        lane_mask, occlusion_masks, occlusion_types
    )
    
    print(f"   检测到 {len(occlusion_analyses)} 个遮挡区域")
    for analysis in occlusion_analyses:
        lane_segment = lane_mask.lane_segments[analysis.lane_id]
        print(f"   - 标线 {analysis.lane_id} ({lane_segment.lane_type.value}) "
              f"被 {analysis.occlusion_type.value} 遮挡 {analysis.overlap_ratio:.2%}, "
              f"置信度: {analysis.confidence:.3f}, 面积: {analysis.occlusion_area}")
    
    # 过滤显著遮挡
    significant_occlusions = detector.filter_significant_occlusions(occlusion_analyses)
    print(f"   其中 {len(significant_occlusions)} 个为显著遮挡")
    
    # 7. 创建上下文信息
    print("7. 创建推理上下文...")
    contexts = []
    for analysis in significant_occlusions:
        lane_segment = lane_mask.lane_segments[analysis.lane_id]
        context = detector.create_occlusion_context(lane_segment, analysis, lane_mask)
        contexts.append(context)
        print(f"   标线 {analysis.lane_id}: 类型={context['lane_type'].value}, "
              f"遮挡比例={context['occlusion_ratio']:.2%}, "
              f"可见长度={context['visible_length']}")
    
    # 8. 规则推理
    print("8. 执行规则推理...")
    inference_results = engine.infer_completions(lane_mask, significant_occlusions, contexts)
    
    print(f"   生成 {len(inference_results)} 个推理结果")
    for result in inference_results:
        lane_segment = lane_mask.lane_segments[result.lane_id]
        print(f"   - 规则 {result.rule_id}: 标线 {result.lane_id} ({lane_segment.lane_type.value}), "
              f"置信度: {result.confidence:.3f}")
    
    # 9. 结构补全
    print("9. 执行结构补全...")
    completion_result = completer.complete_structure(lane_mask, inference_results)
    
    print("   补全统计:")
    stats = completion_result.statistics
    print(f"   - 总推理数: {stats['total_inferences']}")
    print(f"   - 成功补全: {stats['successful_completions']}")
    print(f"   - 失败补全: {stats['failed_completions']}")
    print(f"   - 补全像素: {stats['total_completed_pixels']}")
    if stats['successful_completions'] > 0:
        print(f"   - 平均置信度: {stats['average_confidence']:.3f}")
    
    # 10. 生成可视化
    print("10. 生成可视化结果...")
    
    # 创建详细的RGB可视化
    rgb_detailed = mask_processor.create_detailed_rgb_visualization(
        lane_mask, completion_result.completed_mask, completion_result.completion_regions
    )
    
    # 创建对比图像
    comparison_image = mask_processor.create_comparison_image(
        image, completion_result.original_mask, 
        completion_result.completed_mask, completion_result.completion_regions
    )
    
    # 11. 保存结果
    print("11. 保存结果...")
    output_dir = "examples/output_real_simple"
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存图像
    cv2.imwrite(f"{output_dir}/scaled_image.jpg", image)
    cv2.imwrite(f"{output_dir}/scaled_mask.png", original_mask)
    cv2.imwrite(f"{output_dir}/rgb_visualization.jpg", rgb_detailed)
    cv2.imwrite(f"{output_dir}/comparison_view.jpg", comparison_image)
    
    # 保存遮挡区域
    cv2.imwrite(f"{output_dir}/car_occlusion.png", car_mask * 255)
    
    # 保存各个标线类型的mask
    for name, mask in lane_masks.items():
        cv2.imwrite(f"{output_dir}/lane_{name}.png", mask * 255)
    
    # 保存补全区域
    if np.any(completion_result.completion_regions):
        cv2.imwrite(f"{output_dir}/completion_regions.png", 
                   completion_result.completion_regions * 255)
    
    # 生成详细报告
    report = completer.generate_completion_report(completion_result)
    
    # 添加测试信息
    report['test_info'] = {
        'original_image_size': f"{image.shape[0]*4}x{image.shape[1]*4}",
        'scaled_image_size': f"{image.shape[0]}x{image.shape[1]}",
        'scale_factor': 0.25,
        'detected_lane_types': {name: lane_type.value for name, lane_type in lane_types.items()},
        'occlusion_info': {
            'car_occlusion_pixels': int(np.sum(car_mask)),
            'total_lane_pixels': int(np.sum(original_mask > 0))
        }
    }
    
    # 保存报告
    import json
    with open(f"{output_dir}/detailed_report.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"   结果已保存到: {output_dir}")
    
    # 12. 显示总结
    print("\n=== 真实数据简化测试总结 ===")
    print(f"数据信息:")
    print(f"  - 缩放后图像尺寸: {image.shape}")
    print(f"  - 检测到标线类型: {len(lane_types)}")
    print(f"  - 遮挡区域: {len(occlusion_masks)}")
    print(f"  - 检测到的遮挡: {len(occlusion_analyses)}")
    print(f"  - 显著遮挡: {len(significant_occlusions)}")
    
    print(f"处理结果:")
    print(f"  - 匹配规则数: {len(inference_results)}")
    print(f"  - 成功补全: {stats['successful_completions']}")
    print(f"  - 补全像素: {stats['total_completed_pixels']}")
    
    if len(occlusion_analyses) > 0:
        print(f"遮挡详情:")
        for analysis in occlusion_analyses:
            lane_segment = lane_mask.lane_segments[analysis.lane_id]
            print(f"  - {lane_segment.lane_type.value}: {analysis.overlap_ratio:.1%} 被遮挡")
    
    print("\n真实数据简化测试完成！")
    return completion_result, report


if __name__ == "__main__":
    try:
        completion_result, report = run_real_data_simple_test()
        if completion_result is not None:
            print("\n测试运行成功！")
        
    except Exception as e:
        print(f"测试运行出错: {e}")
        import traceback
        traceback.print_exc()
