"""
调试标线和遮挡物的空间关系
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import cv2
from src.utils.mask_processor import MaskProcessor

def debug_spatial_relationship():
    """调试标线和遮挡物的空间关系"""
    print("=== 调试标线和遮挡物的空间关系 ===")
    
    # 1. 加载数据
    image_path = "data/images/DJI_20220915153843_0320.JPG"
    mask_path = "data/masks/DJI_20220915153843_0320.png"
    
    image = cv2.imread(image_path)
    mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
    
    # 缩放
    scale_factor = 0.25
    image = cv2.resize(image, None, fx=scale_factor, fy=scale_factor)
    mask = cv2.resize(mask, None, fx=scale_factor, fy=scale_factor, interpolation=cv2.INTER_NEAREST)
    
    print(f"图像尺寸: {image.shape}")
    
    # 2. 解析标线
    processor = MaskProcessor()
    lane_masks, lane_types, _, _ = processor.parse_grayscale_mask(mask)
    
    # 3. 创建遮挡物mask
    occlusion_pixels = [1, 2, 11, 12]
    unified_occlusion_mask = np.zeros_like(mask)
    for pixel_value in occlusion_pixels:
        unified_occlusion_mask[mask == pixel_value] = 1
    
    print(f"遮挡物像素总数: {np.sum(unified_occlusion_mask)}")
    
    # 4. 检查每种标线与遮挡物的关系
    print("\n标线与遮挡物的空间关系:")
    
    total_overlaps = 0
    total_adjacencies = 0
    
    for lane_name, lane_mask in lane_masks.items():
        lane_type = lane_types[lane_name]
        
        # 直接重叠
        direct_overlap = np.logical_and(lane_mask, unified_occlusion_mask)
        overlap_pixels = np.sum(direct_overlap)
        
        # 邻接关系（膨胀检测）
        kernel = np.ones((10, 10), np.uint8)  # 10像素邻接范围
        dilated_lane = cv2.dilate(lane_mask.astype(np.uint8), kernel, iterations=1)
        adjacency = np.logical_and(dilated_lane, unified_occlusion_mask)
        adjacency_pixels = np.sum(adjacency)
        
        print(f"  {lane_name} ({lane_type.value}):")
        print(f"    标线像素: {np.sum(lane_mask):,}")
        print(f"    直接重叠: {overlap_pixels:,} 像素")
        print(f"    邻接关系: {adjacency_pixels:,} 像素")
        
        if overlap_pixels > 0:
            total_overlaps += 1
        if adjacency_pixels > 0:
            total_adjacencies += 1
    
    print(f"\n总结:")
    print(f"  有直接重叠的标线: {total_overlaps}")
    print(f"  有邻接关系的标线: {total_adjacencies}")
    
    # 5. 创建综合可视化
    output_dir = "examples/output_debug_spatial"
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建多层可视化
    h, w = image.shape[:2]
    comprehensive_vis = np.zeros((h * 2, w * 2, 3), dtype=np.uint8)
    
    # 左上：原图
    comprehensive_vis[:h, :w] = image
    
    # 右上：所有标线
    all_lanes_vis = np.zeros((h, w, 3), dtype=np.uint8)
    colors = [(255, 255, 255), (200, 200, 200), (255, 255, 0), (0, 255, 0), 
              (0, 255, 255), (255, 0, 255), (128, 128, 128), (255, 128, 0)]
    
    for i, (lane_name, lane_mask) in enumerate(lane_masks.items()):
        color = colors[i % len(colors)]
        all_lanes_vis[lane_mask > 0] = color
    
    comprehensive_vis[:h, w:] = all_lanes_vis
    
    # 左下：遮挡物
    occlusion_vis = np.zeros((h, w, 3), dtype=np.uint8)
    
    # 不同遮挡物用不同颜色
    occlusion_colors = {1: [255, 0, 0], 2: [0, 255, 0], 11: [0, 0, 255], 12: [255, 255, 0]}
    for pixel_value, color in occlusion_colors.items():
        occlusion_vis[mask == pixel_value] = color
    
    comprehensive_vis[h:, :w] = occlusion_vis
    
    # 右下：重叠和邻接关系
    relationship_vis = all_lanes_vis.copy()
    
    # 添加遮挡物（半透明）
    for pixel_value, color in occlusion_colors.items():
        occlusion_mask_single = (mask == pixel_value)
        relationship_vis[occlusion_mask_single] = np.array(color) // 2  # 半透明效果
    
    # 标记重叠区域
    for lane_name, lane_mask in lane_masks.items():
        direct_overlap = np.logical_and(lane_mask, unified_occlusion_mask)
        relationship_vis[direct_overlap] = [255, 255, 255]  # 白色表示重叠
    
    comprehensive_vis[h:, w:] = relationship_vis
    
    # 添加标题
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(comprehensive_vis, "Original Image", (10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(comprehensive_vis, "Lane Markings", (w + 10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(comprehensive_vis, "Occlusions", (10, h + 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(comprehensive_vis, "Relationships", (w + 10, h + 25), font, 0.7, (255, 255, 255), 2)
    
    cv2.imwrite(f"{output_dir}/spatial_relationship_analysis.jpg", comprehensive_vis)
    
    # 6. 为每种标线创建单独的邻接分析
    for lane_name, lane_mask in lane_masks.items():
        lane_type = lane_types[lane_name]
        
        # 创建该标线的邻接可视化
        lane_adjacency_vis = np.zeros((h, w, 3), dtype=np.uint8)
        
        # 标线（白色）
        lane_adjacency_vis[lane_mask > 0] = [255, 255, 255]
        
        # 遮挡物（红色）
        lane_adjacency_vis[unified_occlusion_mask > 0] = [255, 0, 0]
        
        # 邻接区域（黄色）
        kernel = np.ones((10, 10), np.uint8)
        dilated_lane = cv2.dilate(lane_mask.astype(np.uint8), kernel, iterations=1)
        adjacency = np.logical_and(dilated_lane, unified_occlusion_mask)
        lane_adjacency_vis[adjacency] = [0, 255, 255]
        
        # 直接重叠（绿色）
        direct_overlap = np.logical_and(lane_mask, unified_occlusion_mask)
        lane_adjacency_vis[direct_overlap] = [0, 255, 0]
        
        cv2.imwrite(f"{output_dir}/adjacency_{lane_name}.jpg", lane_adjacency_vis)
    
    print(f"\n可视化已保存到: {output_dir}")
    
    # 7. 检查是否需要修改触发逻辑
    if total_overlaps == 0 and total_adjacencies > 0:
        print("\n💡 发现问题：")
        print("   - 没有直接重叠，但有邻接关系")
        print("   - 这符合您的理解：遮挡物不一定完全遮挡标线")
        print("   - 需要基于邻接关系而非重叠来触发补全")
    elif total_overlaps == 0 and total_adjacencies == 0:
        print("\n💡 发现问题：")
        print("   - 既没有重叠也没有邻接关系")
        print("   - 可能需要增大邻接检测范围")
        print("   - 或者这个数据集中确实没有需要补全的情况")

if __name__ == "__main__":
    debug_spatial_relationship()
