"""
真实数据遮挡测试
确保遮挡区域与标线重合，测试补全算法
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import cv2
from src.models.mask_model import LaneMask, LaneType, OcclusionType
from src.core.occlusion_detector import OcclusionDetector
from src.core.rule_engine import RuleEngine
from src.core.structure_completer import StructureCompleter
from src.utils.mask_processor import MaskProcessor


def create_targeted_occlusion(original_mask, lane_masks, lane_types):
    """
    创建针对特定标线类型的遮挡

    Args:
        original_mask: 原始标线mask
        lane_masks: 各类型标线mask字典
        lane_types: 标线类型字典

    Returns:
        (occlusion_masks, occlusion_types)
    """
    occlusion_masks = {}
    occlusion_types = {}

    # 1. 对单实线创建遮挡
    if 'solid_line_3' in lane_masks:
        solid_mask = lane_masks['solid_line_3']
        solid_pixels = np.where(solid_mask > 0)

        if len(solid_pixels[0]) > 100:
            # 选择中间部分创建遮挡
            mid_idx = len(solid_pixels[0]) // 2
            center_y = solid_pixels[0][mid_idx]
            center_x = solid_pixels[1][mid_idx]

            # 创建矩形遮挡
            car_mask = np.zeros_like(original_mask)
            width, height = 80, 50
            y1 = max(0, center_y - height // 2)
            y2 = min(original_mask.shape[0], center_y + height // 2)
            x1 = max(0, center_x - width // 2)
            x2 = min(original_mask.shape[1], center_x + width // 2)
            car_mask[y1:y2, x1:x2] = 1

            # 只保留与标线重合的部分
            car_mask = np.logical_and(car_mask, solid_mask > 0).astype(np.uint8)

            if np.sum(car_mask) > 50:
                occlusion_masks['car_on_solid'] = car_mask
                occlusion_types['car_on_solid'] = OcclusionType.DYNAMIC
                print(f"   创建单实线遮挡: {np.sum(car_mask)} 像素")

    # 2. 对箭头创建遮挡
    if 'arrow_straight_7' in lane_masks:
        arrow_mask = lane_masks['arrow_straight_7']
        arrow_pixels = np.where(arrow_mask > 0)

        if len(arrow_pixels[0]) > 50:
            # 选择箭头中心创建遮挡
            center_y = int(np.mean(arrow_pixels[0]))
            center_x = int(np.mean(arrow_pixels[1]))

            # 创建椭圆遮挡（模拟行人）
            human_mask = np.zeros_like(original_mask)
            cv2.ellipse(human_mask, (center_x, center_y), (25, 35), 0, 0, 360, 1, -1)

            # 只保留与箭头重合的部分
            human_mask = np.logical_and(human_mask, arrow_mask > 0).astype(np.uint8)

            if np.sum(human_mask) > 20:
                occlusion_masks['human_on_arrow'] = human_mask
                occlusion_types['human_on_arrow'] = OcclusionType.DYNAMIC
                print(f"   创建箭头遮挡: {np.sum(human_mask)} 像素")

    # 3. 对虚线创建遮挡
    if 'dashed_line_4' in lane_masks:
        dashed_mask = lane_masks['dashed_line_4']
        dashed_pixels = np.where(dashed_mask > 0)

        if len(dashed_pixels[0]) > 50:
            # 选择虚线的一段创建遮挡
            quarter_idx = len(dashed_pixels[0]) // 4
            center_y = dashed_pixels[0][quarter_idx]
            center_x = dashed_pixels[1][quarter_idx]

            # 创建小车遮挡
            small_car_mask = np.zeros_like(original_mask)
            width, height = 60, 35
            y1 = max(0, center_y - height // 2)
            y2 = min(original_mask.shape[0], center_y + height // 2)
            x1 = max(0, center_x - width // 2)
            x2 = min(original_mask.shape[1], center_x + width // 2)
            small_car_mask[y1:y2, x1:x2] = 1

            # 只保留与虚线重合的部分
            small_car_mask = np.logical_and(small_car_mask, dashed_mask > 0).astype(np.uint8)

            if np.sum(small_car_mask) > 30:
                occlusion_masks['car_on_dashed'] = small_car_mask
                occlusion_types['car_on_dashed'] = OcclusionType.DYNAMIC
                print(f"   创建虚线遮挡: {np.sum(small_car_mask)} 像素")

    return occlusion_masks, occlusion_types


def run_real_data_with_occlusion():
    """运行真实数据遮挡测试"""
    print("=== 真实数据遮挡测试 ===")

    # 1. 加载并缩放真实数据
    print("1. 加载并缩放真实数据...")
    image_path = "data/images/DJI_20220915153843_0320.JPG"
    mask_path = "data/masks/DJI_20220915153843_0320.png"

    try:
        image = cv2.imread(image_path)
        original_mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)

        if image is None or original_mask is None:
            print(f"   错误：无法加载数据文件")
            return None, None

        # 缩放到1/4大小
        scale_factor = 0.25
        image = cv2.resize(image, None, fx=scale_factor, fy=scale_factor)
        original_mask = cv2.resize(original_mask, None, fx=scale_factor, fy=scale_factor, interpolation=cv2.INTER_NEAREST)

        print(f"   缩放后图像尺寸: {image.shape}")
        print(f"   Mask像素值: {np.unique(original_mask)}")

    except Exception as e:
        print(f"   加载数据时出错: {e}")
        return None, None

    # 2. 解析标线数据
    print("2. 解析标线数据...")
    mask_processor = MaskProcessor()
    lane_masks, lane_types, _, _ = mask_processor.parse_grayscale_mask(original_mask)

    print(f"   检测到 {len(lane_masks)} 种标线类型:")
    for name, lane_type in lane_types.items():
        pixels = np.sum(lane_masks[name])
        print(f"   - {name}: {lane_type.value} ({pixels} 像素)")

    # 3. 创建针对性遮挡
    print("3. 创建针对性遮挡...")
    occlusion_masks, occlusion_types = create_targeted_occlusion(original_mask, lane_masks, lane_types)

    if len(occlusion_masks) == 0:
        print("   没有创建任何遮挡区域")
        return None, None

    # 4. 初始化处理器（降低阈值以便检测到小的遮挡）
    print("4. 初始化处理器...")
    detector = OcclusionDetector({
        'confidence_threshold': 0.05,
        'overlap_threshold': 0.01,  # 降低到1%
        'min_occlusion_area': 10    # 降低最小面积
    })
    engine = RuleEngine()
    completer = StructureCompleter()

    # 5. 创建标线mask对象
    print("5. 创建标线mask对象...")
    lane_mask = LaneMask(image.shape[:2])

    for name, mask in lane_masks.items():
        lane_type = lane_types[name]
        lane_id = lane_mask.add_lane_segment(lane_type, mask)
        print(f"   添加标线段: {name} (ID: {lane_id})")

    # 6. 检测遮挡
    print("6. 检测遮挡...")
    occlusion_analyses = detector.detect_occlusions(
        lane_mask, occlusion_masks, occlusion_types
    )

    print(f"   检测到 {len(occlusion_analyses)} 个遮挡区域")
    for analysis in occlusion_analyses:
        lane_segment = lane_mask.lane_segments[analysis.lane_id]
        print(f"   - 标线 {analysis.lane_id} ({lane_segment.lane_type.value}) "
              f"被 {analysis.occlusion_type.value} 遮挡 {analysis.overlap_ratio:.2%}, "
              f"置信度: {analysis.confidence:.3f}")

    # 过滤显著遮挡
    significant_occlusions = detector.filter_significant_occlusions(occlusion_analyses)
    print(f"   其中 {len(significant_occlusions)} 个为显著遮挡")

    # 7. 创建上下文信息
    print("7. 创建推理上下文...")
    contexts = []
    for analysis in significant_occlusions:
        lane_segment = lane_mask.lane_segments[analysis.lane_id]
        context = detector.create_occlusion_context(lane_segment, analysis, lane_mask)
        contexts.append(context)
        print(f"   标线 {analysis.lane_id}: 类型={context['lane_type'].value}, "
              f"遮挡比例={context['occlusion_ratio']:.2%}, "
              f"可见长度={context['visible_length']}")

    # 8. 规则推理
    print("8. 执行规则推理...")
    inference_results = engine.infer_completions(lane_mask, significant_occlusions, contexts)

    print(f"   生成 {len(inference_results)} 个推理结果")
    for result in inference_results:
        lane_segment = lane_mask.lane_segments[result.lane_id]
        print(f"   - 规则 {result.rule_id}: 标线 {result.lane_id} ({lane_segment.lane_type.value}), "
              f"置信度: {result.confidence:.3f}")
        print(f"     补全像素: {np.sum(result.completed_mask)}")

    # 9. 结构补全
    print("9. 执行结构补全...")
    completion_result = completer.complete_structure(lane_mask, inference_results)

    print("   补全统计:")
    stats = completion_result.statistics
    print(f"   - 总推理数: {stats['total_inferences']}")
    print(f"   - 成功补全: {stats['successful_completions']}")
    print(f"   - 失败补全: {stats['failed_completions']}")
    print(f"   - 补全像素: {stats['total_completed_pixels']}")
    if stats['successful_completions'] > 0:
        print(f"   - 平均置信度: {stats['average_confidence']:.3f}")
        print(f"   - 按类型补全: {stats['completion_by_type']}")

    # 10. 生成可视化
    print("10. 生成可视化结果...")

    # 创建详细的RGB可视化
    rgb_detailed = mask_processor.create_detailed_rgb_visualization(
        lane_mask, completion_result.completed_mask, completion_result.completion_regions
    )

    # 创建对比图像
    comparison_image = mask_processor.create_comparison_image(
        image, completion_result.original_mask,
        completion_result.completed_mask, completion_result.completion_regions
    )

    # 11. 保存结果
    print("11. 保存结果...")
    output_dir = "examples/output_real_occlusion"
    os.makedirs(output_dir, exist_ok=True)

    # 保存图像
    cv2.imwrite(f"{output_dir}/scaled_image.jpg", image)
    cv2.imwrite(f"{output_dir}/original_mask.png", original_mask)
    cv2.imwrite(f"{output_dir}/rgb_visualization.jpg", rgb_detailed)
    cv2.imwrite(f"{output_dir}/comparison_view.jpg", comparison_image)

    # 保存遮挡区域
    for name, mask in occlusion_masks.items():
        cv2.imwrite(f"{output_dir}/occlusion_{name}.png", mask * 255)

    # 保存补全区域
    if np.any(completion_result.completion_regions):
        cv2.imwrite(f"{output_dir}/completion_regions.png",
                   completion_result.completion_regions * 255)

    # 保存推理结果
    for i, result in enumerate(inference_results):
        cv2.imwrite(f"{output_dir}/inference_result_{i}_{result.rule_id}.png",
                   result.completed_mask * 255)

    # 生成详细报告
    report = completer.generate_completion_report(completion_result)

    # 添加测试信息
    report['occlusion_test_info'] = {
        'detected_lane_types': {name: lane_type.value for name, lane_type in lane_types.items()},
        'created_occlusions': {name: int(np.sum(mask)) for name, mask in occlusion_masks.items()},
        'occlusion_analyses': [
            {
                'lane_id': analysis.lane_id,
                'lane_type': lane_mask.lane_segments[analysis.lane_id].lane_type.value,
                'occlusion_type': analysis.occlusion_type.value,
                'overlap_ratio': analysis.overlap_ratio,
                'confidence': analysis.confidence
            }
            for analysis in occlusion_analyses
        ],
        'inference_results': [
            {
                'rule_id': result.rule_id,
                'lane_id': result.lane_id,
                'lane_type': lane_mask.lane_segments[result.lane_id].lane_type.value,
                'confidence': result.confidence,
                'completed_pixels': int(np.sum(result.completed_mask))
            }
            for result in inference_results
        ]
    }

    # 保存报告
    import json
    with open(f"{output_dir}/detailed_report.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False, default=str)

    print(f"   结果已保存到: {output_dir}")

    # 12. 显示总结
    print("\n=== 真实数据遮挡测试总结 ===")
    print(f"数据信息:")
    print(f"  - 图像尺寸: {image.shape}")
    print(f"  - 标线类型: {len(lane_types)}")
    print(f"  - 创建遮挡: {len(occlusion_masks)}")
    print(f"  - 检测遮挡: {len(occlusion_analyses)}")
    print(f"  - 显著遮挡: {len(significant_occlusions)}")

    print(f"处理结果:")
    print(f"  - 匹配规则: {len(inference_results)}")
    print(f"  - 成功补全: {stats['successful_completions']}")
    print(f"  - 补全像素: {stats['total_completed_pixels']}")

    if len(inference_results) > 0:
        print(f"补全详情:")
        for result in inference_results:
            lane_segment = lane_mask.lane_segments[result.lane_id]
            print(f"  - {lane_segment.lane_type.value}: 规则 {result.rule_id}, "
                  f"补全 {np.sum(result.completed_mask)} 像素")

    print("\n真实数据遮挡测试完成！")
    return completion_result, report


if __name__ == "__main__":
    try:
        completion_result, report = run_real_data_with_occlusion()
        if completion_result is not None:
            print("\n测试运行成功！")

    except Exception as e:
        print(f"测试运行出错: {e}")
        import traceback
        traceback.print_exc()
