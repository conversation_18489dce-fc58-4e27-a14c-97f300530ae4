"""
最终完整系统测试
使用修正的补全算法进行完整的邻接补全系统测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import cv2
import json
from src.models.mask_model import LaneMask, LaneType, OcclusionType
from src.core.adjacency_detector import AdjacencyDetector
from src.models.advanced_rule_set import create_advanced_rule_set
from src.utils.mask_processor import MaskProcessor


def run_final_complete_system_test():
    """运行最终完整系统测试"""
    print("=== 最终完整邻接补全系统测试 ===")
    
    # 1. 加载真实数据
    print("1. 加载真实数据...")
    image_path = "data/images/DJI_20220915153843_0320.JPG"
    mask_path = "data/masks/DJI_20220915153843_0320.png"
    
    try:
        image = cv2.imread(image_path)
        mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
        
        if image is None or mask is None:
            print("无法加载数据文件")
            return None
        
        # 缩放数据
        scale_factor = 0.25
        image = cv2.resize(image, None, fx=scale_factor, fy=scale_factor)
        mask = cv2.resize(mask, None, fx=scale_factor, fy=scale_factor, interpolation=cv2.INTER_NEAREST)
        
        print(f"   缩放后图像尺寸: {image.shape}")
        
    except Exception as e:
        print(f"   加载数据时出错: {e}")
        return None
    
    # 2. 解析标线
    print("2. 解析标线数据...")
    processor = MaskProcessor()
    lane_masks, lane_types, _, _ = processor.parse_grayscale_mask(mask)
    
    print(f"   检测到 {len(lane_masks)} 种标线类型:")
    for name, lane_type in lane_types.items():
        pixels = np.sum(lane_masks[name])
        print(f"   - {name}: {lane_type.value} ({pixels} 像素)")
    
    # 3. 创建标线mask对象
    print("3. 创建标线mask对象...")
    lane_mask = LaneMask(image.shape[:2])
    for name, lane_mask_data in lane_masks.items():
        lane_type = lane_types[name]
        lane_id = lane_mask.add_lane_segment(lane_type, lane_mask_data)
    
    # 4. 检测遮挡物和邻接关系
    print("4. 检测遮挡物和邻接关系...")
    detector = AdjacencyDetector({
        'max_gap_distance': 20,
        'min_adjacency_length': 15,
        'confidence_threshold': 0.3
    })
    
    # 检测遮挡物
    occlusion_masks, occlusion_types = detector.detect_occlusions_from_mask(mask)
    print(f"   检测到 {len(occlusion_masks)} 种遮挡物:")
    for name, occlusion_mask in occlusion_masks.items():
        pixels = np.sum(occlusion_mask)
        print(f"   - {name}: {occlusion_types[name].value} ({pixels} 像素)")
    
    # 检测邻接关系
    adjacency_analyses = detector.detect_adjacencies(lane_mask, occlusion_masks, occlusion_types)
    significant_adjacencies = detector.filter_significant_adjacencies(adjacency_analyses)
    
    print(f"   检测到 {len(significant_adjacencies)} 个显著邻接关系")
    
    # 5. 执行补全
    print("5. 执行完整补全流程...")
    rule_set = create_advanced_rule_set()
    
    completion_results = []
    total_completed_pixels = 0
    successful_completions = 0
    
    for i, analysis in enumerate(significant_adjacencies):
        lane_segment = lane_mask.lane_segments[analysis.lane_id]
        occlusion_region = lane_mask.occlusion_regions[analysis.occlusion_id]
        
        print(f"\n   处理邻接关系 {i+1}/{len(significant_adjacencies)}:")
        print(f"   - 标线 {analysis.lane_id} ({lane_segment.lane_type.value})")
        print(f"   - 邻接比例: {analysis.adjacency_ratio:.2%}")
        
        # 创建上下文
        context = detector.create_adjacency_context(lane_segment, analysis, lane_mask)
        
        # 查找匹配规则
        matching_rules = rule_set.find_matching_rules(context)
        
        if len(matching_rules) > 0:
            rule, rule_confidence = matching_rules[0]
            print(f"   - 应用规则: {rule.rule_id} (置信度: {rule_confidence:.3f})")
            
            # 执行补全
            if len(rule.actions) > 0:
                action = rule.actions[0]
                
                try:
                    completed_mask = action.execute(lane_segment, occlusion_region, context)
                    completed_pixels = np.sum(completed_mask)
                    
                    if completed_pixels > 0:
                        successful_completions += 1
                        total_completed_pixels += completed_pixels
                        
                        completion_results.append({
                            'lane_id': analysis.lane_id,
                            'occlusion_id': analysis.occlusion_id,
                            'rule_id': rule.rule_id,
                            'completed_mask': completed_mask,
                            'completed_pixels': completed_pixels,
                            'confidence': rule_confidence,
                            'lane_type': lane_segment.lane_type.value,
                            'adjacency_ratio': analysis.adjacency_ratio
                        })
                        
                        print(f"   ✅ 补全成功: {completed_pixels} 像素")
                    else:
                        print(f"   ❌ 补全失败: 0 像素")
                        
                except Exception as e:
                    print(f"   ❌ 补全出错: {e}")
    
    # 6. 生成最终可视化
    print("6. 生成最终可视化...")
    output_dir = "examples/output_final_complete"
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建最终的补全可视化
    final_visualization = create_final_completion_visualization(
        image, mask, lane_mask, occlusion_masks, significant_adjacencies, completion_results
    )
    cv2.imwrite(f"{output_dir}/final_completion_visualization.jpg", final_visualization)
    
    # 创建补全前后对比
    before_after_comparison = create_before_after_comparison(
        image, mask, completion_results
    )
    cv2.imwrite(f"{output_dir}/before_after_comparison.jpg", before_after_comparison)
    
    # 保存各个补全结果
    for i, result in enumerate(completion_results):
        cv2.imwrite(f"{output_dir}/completion_{i}_{result['rule_id']}.png", 
                   result['completed_mask'] * 255)
    
    # 7. 生成最终报告
    print("7. 生成最终报告...")
    final_report = {
        'system_performance': {
            'total_adjacencies': len(significant_adjacencies),
            'successful_completions': successful_completions,
            'success_rate': successful_completions / len(significant_adjacencies) if significant_adjacencies else 0,
            'total_completed_pixels': int(total_completed_pixels),
            'average_pixels_per_completion': int(total_completed_pixels / max(successful_completions, 1))
        },
        'data_summary': {
            'image_size': image.shape,
            'lane_types_detected': len(lane_types),
            'occlusion_types_detected': len(occlusion_masks),
            'lane_pixels': {name: int(np.sum(mask)) for name, mask in lane_masks.items()},
            'occlusion_pixels': {name: int(np.sum(mask)) for name, mask in occlusion_masks.items()}
        },
        'completion_details': [
            {
                'completion_id': i,
                'lane_type': result['lane_type'],
                'rule_applied': result['rule_id'],
                'completed_pixels': result['completed_pixels'],
                'confidence': result['confidence'],
                'adjacency_ratio': result['adjacency_ratio']
            }
            for i, result in enumerate(completion_results)
        ],
        'adjacency_analysis': [
            {
                'adjacency_id': i,
                'lane_id': adj.lane_id,
                'lane_type': lane_mask.lane_segments[adj.lane_id].lane_type.value,
                'occlusion_type': adj.occlusion_type.value,
                'adjacency_ratio': adj.adjacency_ratio,
                'confidence': adj.confidence,
                'contact_points': len(adj.contact_points)
            }
            for i, adj in enumerate(significant_adjacencies)
        ]
    }
    
    with open(f"{output_dir}/final_system_report.json", 'w', encoding='utf-8') as f:
        json.dump(final_report, f, indent=2, ensure_ascii=False, default=str)
    
    # 8. 显示最终总结
    print("\n=== 最终完整系统测试总结 ===")
    print(f"🎯 系统性能:")
    print(f"  - 检测到邻接关系: {len(significant_adjacencies)}")
    print(f"  - 成功补全: {successful_completions}")
    print(f"  - 成功率: {successful_completions / len(significant_adjacencies) * 100:.1f}%")
    print(f"  - 总补全像素: {total_completed_pixels:,}")
    print(f"  - 平均每次补全: {total_completed_pixels // max(successful_completions, 1):,} 像素")
    
    print(f"\n📊 数据统计:")
    print(f"  - 图像尺寸: {image.shape}")
    print(f"  - 标线类型: {len(lane_types)}")
    print(f"  - 遮挡物类型: {len(occlusion_masks)}")
    
    print(f"\n✅ 补全详情:")
    for i, result in enumerate(completion_results):
        print(f"  {i+1}. {result['lane_type']}: {result['completed_pixels']:,} 像素 "
              f"(规则: {result['rule_id']}, 置信度: {result['confidence']:.3f})")
    
    print(f"\n🎉 最终完整系统测试成功完成！")
    print(f"📁 结果已保存到: {output_dir}")
    
    return final_report


def create_final_completion_visualization(image, mask, lane_mask, occlusion_masks, 
                                        adjacencies, completion_results):
    """创建最终补全可视化"""
    h, w = image.shape[:2]
    
    # 创建2x3布局
    result = np.zeros((h * 2, w * 3, 3), dtype=np.uint8)
    
    # 第一行：原图、标线、遮挡物
    result[:h, :w] = image
    
    # 标线可视化
    lane_vis = np.zeros((h, w, 3), dtype=np.uint8)
    for lane_segment in lane_mask.lane_segments.values():
        lane_vis[lane_segment.mask > 0] = [255, 255, 255]
    result[:h, w:w*2] = lane_vis
    
    # 遮挡物可视化
    occlusion_vis = np.zeros((h, w, 3), dtype=np.uint8)
    colors = {'car': [255, 0, 0], 'human': [0, 255, 0], 'clatter': [0, 0, 255], 'obstacle': [255, 255, 0]}
    for name, occlusion_mask in occlusion_masks.items():
        color = colors.get(name, [128, 128, 128])
        occlusion_vis[occlusion_mask > 0] = color
    result[:h, w*2:] = occlusion_vis
    
    # 第二行：邻接关系、补全结果、最终结果
    # 邻接关系
    adjacency_vis = lane_vis.copy()
    for name, occlusion_mask in occlusion_masks.items():
        color = colors.get(name, [128, 128, 128])
        adjacency_vis[occlusion_mask > 0] = color
    
    # 标记邻接点
    for adjacency in adjacencies:
        for point in adjacency.contact_points[:3]:  # 只显示前3个点
            x, y = point
            if 0 <= x < w and 0 <= y < h:
                cv2.circle(adjacency_vis, (x, y), 2, (255, 0, 255), -1)
    
    result[h:, :w] = adjacency_vis
    
    # 补全结果
    completion_vis = np.zeros((h, w, 3), dtype=np.uint8)
    for i, comp_result in enumerate(completion_results):
        color = [(i * 60) % 255, (i * 120) % 255, (i * 180) % 255]
        completion_vis[comp_result['completed_mask'] > 0] = color
    result[h:, w:w*2] = completion_vis
    
    # 最终结果（原始标线 + 补全）
    final_vis = lane_vis.copy()
    for comp_result in completion_results:
        final_vis[comp_result['completed_mask'] > 0] = [0, 255, 0]  # 绿色补全
    result[h:, w*2:] = final_vis
    
    # 添加标题
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(result, "Original Image", (10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Lane Markings", (w + 10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Occlusions", (w*2 + 10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Adjacencies", (10, h + 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Completions", (w + 10, h + 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Final Result", (w*2 + 10, h + 25), font, 0.7, (255, 255, 255), 2)
    
    return result


def create_before_after_comparison(image, original_mask, completion_results):
    """创建补全前后对比"""
    h, w = image.shape[:2]
    result = np.zeros((h, w * 2, 3), dtype=np.uint8)
    
    # 左侧：原始图像 + 原始标线
    left_vis = image.copy()
    
    # 右侧：原始图像 + 原始标线 + 补全
    right_vis = image.copy()
    
    # 在图像上叠加标线（半透明）
    overlay_left = np.zeros_like(image)
    overlay_right = np.zeros_like(image)
    
    # 原始标线（白色）
    overlay_left[original_mask > 0] = [255, 255, 255]
    overlay_right[original_mask > 0] = [255, 255, 255]
    
    # 补全标线（绿色）
    for comp_result in completion_results:
        overlay_right[comp_result['completed_mask'] > 0] = [0, 255, 0]
    
    # 混合图像
    alpha = 0.6
    left_vis = cv2.addWeighted(left_vis, 1-alpha, overlay_left, alpha, 0)
    right_vis = cv2.addWeighted(right_vis, 1-alpha, overlay_right, alpha, 0)
    
    result[:, :w] = left_vis
    result[:, w:] = right_vis
    
    # 添加标题
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(result, "Before Completion", (10, 30), font, 1.0, (255, 255, 255), 2)
    cv2.putText(result, "After Completion", (w + 10, 30), font, 1.0, (255, 255, 255), 2)
    
    return result


if __name__ == "__main__":
    try:
        final_report = run_final_complete_system_test()
        if final_report is not None:
            print("\n🎉 最终完整系统测试运行成功！")
        
    except Exception as e:
        print(f"最终测试运行出错: {e}")
        import traceback
        traceback.print_exc()
