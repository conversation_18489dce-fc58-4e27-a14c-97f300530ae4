"""
调试补全可视化
检查为什么补全结果没有在对比视图中显示
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import cv2
from src.utils.mask_processor import MaskProcessor


def debug_completion_visualization():
    """调试补全可视化"""
    print("=== 调试补全可视化 ===")
    
    # 1. 加载所有相关文件
    print("1. 加载文件...")
    
    # 加载原始图像和mask
    image = cv2.imread("examples/output_real_occlusion/scaled_image.jpg")
    original_mask = cv2.imread("examples/output_real_occlusion/original_mask.png", cv2.IMREAD_GRAYSCALE)
    
    # 加载补全相关文件
    completion_regions = cv2.imread("examples/output_real_occlusion/completion_regions.png", cv2.IMREAD_GRAYSCALE)
    inference_result = cv2.imread("examples/output_real_occlusion/inference_result_0_dashed_line_pattern_001.png", cv2.IMREAD_GRAYSCALE)
    
    print(f"   图像: {image.shape if image is not None else '无法加载'}")
    print(f"   原始mask: {original_mask.shape if original_mask is not None else '无法加载'}")
    print(f"   补全区域: {completion_regions.shape if completion_regions is not None else '无法加载'}")
    print(f"   推理结果: {inference_result.shape if inference_result is not None else '无法加载'}")
    
    if any(x is None for x in [image, original_mask, completion_regions, inference_result]):
        print("   错误：无法加载所有必需文件")
        return
    
    # 2. 检查数据
    print("2. 检查数据...")
    print(f"   原始mask像素值: {np.unique(original_mask)}")
    print(f"   原始mask非零像素: {np.sum(original_mask > 0)}")
    print(f"   补全区域像素值: {np.unique(completion_regions)}")
    print(f"   补全区域非零像素: {np.sum(completion_regions > 0)}")
    print(f"   推理结果像素值: {np.unique(inference_result)}")
    print(f"   推理结果非零像素: {np.sum(inference_result > 0)}")
    
    # 3. 创建完整的补全mask
    print("3. 创建完整的补全mask...")
    
    # 将补全区域转换为二值
    completion_binary = (completion_regions > 0).astype(np.uint8)
    inference_binary = (inference_result > 0).astype(np.uint8)
    
    # 创建完整的补全后mask
    original_binary = (original_mask > 0).astype(np.uint8)
    completed_mask = np.logical_or(original_binary, completion_binary).astype(np.uint8)
    
    print(f"   原始二值mask像素: {np.sum(original_binary)}")
    print(f"   补全二值mask像素: {np.sum(completion_binary)}")
    print(f"   完整补全mask像素: {np.sum(completed_mask)}")
    print(f"   新增像素: {np.sum(completed_mask) - np.sum(original_binary)}")
    
    # 4. 创建可视化
    print("4. 创建可视化...")
    
    # 创建高对比度的可视化
    h, w = image.shape[:2]
    
    # 创建2x2布局
    result = np.zeros((h * 2, w * 2, 3), dtype=np.uint8)
    
    # 左上：原始图像
    result[:h, :w] = image
    
    # 右上：原始mask（白色）
    original_vis = np.zeros((h, w, 3), dtype=np.uint8)
    original_vis[original_binary > 0] = [255, 255, 255]  # 白色
    result[:h, w:] = original_vis
    
    # 左下：补全结果（原始=白色，补全=红色）
    completed_vis = np.zeros((h, w, 3), dtype=np.uint8)
    completed_vis[original_binary > 0] = [255, 255, 255]  # 原始标线白色
    completed_vis[completion_binary > 0] = [0, 0, 255]    # 补全区域红色
    result[h:, :w] = completed_vis
    
    # 右下：只显示补全区域（高亮红色）
    completion_only_vis = np.zeros((h, w, 3), dtype=np.uint8)
    completion_only_vis[completion_binary > 0] = [0, 0, 255]  # 红色
    # 为了更容易看到，我们放大补全区域
    if np.sum(completion_binary) > 0:
        # 膨胀操作使补全区域更明显
        kernel = np.ones((5, 5), np.uint8)
        dilated_completion = cv2.dilate(completion_binary, kernel, iterations=2)
        completion_only_vis[dilated_completion > 0] = [0, 255, 0]  # 绿色膨胀区域
        completion_only_vis[completion_binary > 0] = [0, 0, 255]   # 红色原始补全
    result[h:, w:] = completion_only_vis
    
    # 添加标题
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(result, "Original Image", (10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Original Mask", (w + 10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "With Completion (Red)", (10, h + 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Completion Only (Red+Green)", (w + 10, h + 25), font, 0.7, (255, 255, 255), 2)
    
    # 5. 保存调试结果
    print("5. 保存调试结果...")
    output_dir = "examples/output_real_occlusion"
    
    cv2.imwrite(f"{output_dir}/debug_completion_visualization.jpg", result)
    cv2.imwrite(f"{output_dir}/debug_original_binary.png", original_binary * 255)
    cv2.imwrite(f"{output_dir}/debug_completion_binary.png", completion_binary * 255)
    cv2.imwrite(f"{output_dir}/debug_completed_mask.png", completed_mask * 255)
    
    # 创建放大的补全区域视图
    if np.sum(completion_binary) > 0:
        # 找到补全区域的边界框
        coords = np.where(completion_binary > 0)
        if len(coords[0]) > 0:
            y_min, y_max = np.min(coords[0]), np.max(coords[0])
            x_min, x_max = np.min(coords[1]), np.max(coords[1])
            
            # 扩展边界框
            margin = 50
            y_min = max(0, y_min - margin)
            y_max = min(h, y_max + margin)
            x_min = max(0, x_min - margin)
            x_max = min(w, x_max + margin)
            
            # 裁剪区域
            crop_image = image[y_min:y_max, x_min:x_max]
            crop_original = original_binary[y_min:y_max, x_min:x_max]
            crop_completion = completion_binary[y_min:y_max, x_min:x_max]
            
            # 创建放大视图
            crop_h, crop_w = crop_image.shape[:2]
            crop_result = np.zeros((crop_h, crop_w * 3, 3), dtype=np.uint8)
            
            # 原图
            crop_result[:, :crop_w] = crop_image
            
            # 原始mask
            crop_original_vis = np.zeros((crop_h, crop_w, 3), dtype=np.uint8)
            crop_original_vis[crop_original > 0] = [255, 255, 255]
            crop_result[:, crop_w:crop_w*2] = crop_original_vis
            
            # 补全结果
            crop_completion_vis = np.zeros((crop_h, crop_w, 3), dtype=np.uint8)
            crop_completion_vis[crop_original > 0] = [255, 255, 255]  # 原始白色
            crop_completion_vis[crop_completion > 0] = [0, 0, 255]    # 补全红色
            crop_result[:, crop_w*2:] = crop_completion_vis
            
            cv2.imwrite(f"{output_dir}/debug_completion_closeup.jpg", crop_result)
            print(f"   补全区域位置: ({x_min}, {y_min}) 到 ({x_max}, {y_max})")
    
    print(f"   调试结果已保存到: {output_dir}")
    
    # 6. 分析问题
    print("6. 问题分析...")
    
    if np.sum(completion_binary) == 0:
        print("   问题：补全区域为空")
    elif np.sum(completion_binary) < 100:
        print(f"   问题：补全区域太小 ({np.sum(completion_binary)} 像素)，在缩放图像中难以看见")
    else:
        print("   补全区域大小正常")
    
    # 检查补全区域是否与原始标线重叠
    overlap = np.logical_and(original_binary, completion_binary)
    if np.sum(overlap) > 0:
        print(f"   注意：补全区域与原始标线重叠 {np.sum(overlap)} 像素")
    
    print("\n调试完成！请查看生成的调试图像。")


if __name__ == "__main__":
    debug_completion_visualization()
