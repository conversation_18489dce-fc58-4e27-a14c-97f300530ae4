"""
调试断裂检测
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import cv2
from src.core.lane_entity_analyzer import LaneEntityAnalyzer
from src.utils.mask_processor import MaskProcessor

def debug_break_detection():
    """调试断裂检测"""
    print("=== 调试断裂检测 ===")
    
    # 1. 加载数据
    image_path = "data/images/DJI_20220915153843_0320.JPG"
    mask_path = "data/masks/DJI_20220915153843_0320.png"
    
    image = cv2.imread(image_path)
    mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
    
    # 缩放
    scale_factor = 0.25
    image = cv2.resize(image, None, fx=scale_factor, fy=scale_factor)
    mask = cv2.resize(mask, None, fx=scale_factor, fy=scale_factor, interpolation=cv2.INTER_NEAREST)
    
    print(f"图像尺寸: {image.shape}")
    
    # 2. 解析标线
    processor = MaskProcessor()
    lane_masks, lane_types, _, _ = processor.parse_grayscale_mask(mask)
    
    # 3. 创建遮挡物mask
    occlusion_pixels = [1, 2, 11, 12]
    unified_occlusion_mask = np.zeros_like(mask)
    for pixel_value in occlusion_pixels:
        unified_occlusion_mask[mask == pixel_value] = 1
    
    print(f"遮挡物像素总数: {np.sum(unified_occlusion_mask)}")
    
    # 4. 分析第一个标线实体
    analyzer = LaneEntityAnalyzer()
    
    # 只分析第一个标线
    first_lane_name = list(lane_masks.keys())[0]
    first_lane_mask = lane_masks[first_lane_name]
    first_lane_type = lane_types[first_lane_name]
    
    print(f"\n分析标线: {first_lane_name} ({first_lane_type.value})")
    print(f"标线像素数: {np.sum(first_lane_mask)}")
    
    # 分析标线实体
    entity = analyzer._analyze_single_lane_entity(first_lane_mask, first_lane_type)
    
    if entity is None:
        print("无法分析标线实体")
        return
    
    print(f"中心线长度: {len(entity.center_line)}")
    print(f"平均宽度: {np.mean(entity.width_profile):.1f}")
    
    # 5. 手动检查中心线与遮挡物的交叉
    print("\n检查中心线与遮挡物的交叉:")
    
    center_points = entity.center_line
    intersections = 0
    
    for i, point in enumerate(center_points):
        x, y = point
        if 0 <= x < unified_occlusion_mask.shape[1] and 0 <= y < unified_occlusion_mask.shape[0]:
            if unified_occlusion_mask[y, x] > 0:
                intersections += 1
                if intersections <= 5:  # 只打印前5个交叉点
                    print(f"  交叉点 {intersections}: ({x}, {y})")
    
    print(f"总交叉点数: {intersections}")
    
    # 6. 检测断裂
    breaks = analyzer._detect_occlusion_breaks(entity, unified_occlusion_mask, 0)
    print(f"检测到断裂数: {len(breaks)}")
    
    for i, break_obj in enumerate(breaks):
        print(f"  断裂 {i+1}: {break_obj.break_start} -> {break_obj.break_end}")
    
    # 7. 创建可视化
    output_dir = "examples/output_debug_break"
    os.makedirs(output_dir, exist_ok=True)
    
    # 可视化中心线
    center_vis = np.zeros((*image.shape[:2], 3), dtype=np.uint8)
    for i in range(len(center_points) - 1):
        p1 = center_points[i]
        p2 = center_points[i + 1]
        cv2.line(center_vis, p1, p2, (0, 255, 0), 2)
    
    # 可视化遮挡物
    occlusion_vis = center_vis.copy()
    occlusion_vis[unified_occlusion_mask > 0] = [255, 0, 0]
    
    # 可视化交叉点
    intersection_vis = occlusion_vis.copy()
    for point in center_points:
        x, y = point
        if 0 <= x < unified_occlusion_mask.shape[1] and 0 <= y < unified_occlusion_mask.shape[0]:
            if unified_occlusion_mask[y, x] > 0:
                cv2.circle(intersection_vis, (x, y), 3, (0, 255, 255), -1)
    
    cv2.imwrite(f"{output_dir}/center_line.jpg", center_vis)
    cv2.imwrite(f"{output_dir}/occlusion_overlay.jpg", occlusion_vis)
    cv2.imwrite(f"{output_dir}/intersections.jpg", intersection_vis)
    
    print(f"\n可视化已保存到: {output_dir}")

if __name__ == "__main__":
    debug_break_detection()
