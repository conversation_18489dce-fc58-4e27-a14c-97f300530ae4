"""
主处理流程
整合所有模块，实现完整的遮挡恢复流程
"""

import numpy as np
import cv2
import yaml
from typing import Dict, List, Tuple, Optional
import logging
from pathlib import Path

from .models.mask_model import LaneMask, LaneType, OcclusionType
from .core.occlusion_detector import OcclusionDetector
from .core.rule_engine import RuleEngine
from .core.structure_completer import StructureCompleter
from .utils.visualization import Visualizer
from .utils.io_handler import IOHandler


class OcclusionRecoverySystem:
    """遮挡恢复系统主类"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化系统
        
        Args:
            config_path: 配置文件路径
        """
        # 加载配置
        self.config = self._load_config(config_path)
        
        # 初始化各模块
        self.occlusion_detector = OcclusionDetector(self.config.get('occlusion_detection', {}))
        self.rule_engine = RuleEngine(config=self.config.get('rule_engine', {}))
        self.structure_completer = StructureCompleter(self.config.get('structure_completion', {}))
        self.visualizer = Visualizer(self.config.get('visualization', {}))
        self.io_handler = IOHandler(self.config.get('data', {}))
        
        # 设置日志
        self._setup_logging()
        self.logger = logging.getLogger(__name__)
    
    def _load_config(self, config_path: Optional[str]) -> Dict:
        """加载配置文件"""
        if config_path is None:
            config_path = "configs/config.yaml"
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except FileNotFoundError:
            print(f"Config file {config_path} not found, using default config")
            return {}
        except Exception as e:
            print(f"Error loading config: {e}, using default config")
            return {}
    
    def _setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('occlusion_recovery.log'),
                logging.StreamHandler()
            ]
        )
    
    def process_image(self, 
                     image_path: str,
                     lane_masks: Dict[str, np.ndarray],
                     lane_types: Dict[str, LaneType],
                     occlusion_masks: Dict[str, np.ndarray],
                     occlusion_types: Dict[str, OcclusionType]) -> Dict:
        """
        处理单张图像
        
        Args:
            image_path: 图像路径
            lane_masks: 标线mask字典 {name: mask}
            lane_types: 标线类型字典 {name: type}
            occlusion_masks: 遮挡物mask字典 {name: mask}
            occlusion_types: 遮挡类型字典 {name: type}
            
        Returns:
            处理结果字典
        """
        self.logger.info(f"Processing image: {image_path}")
        
        try:
            # 1. 加载图像
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Cannot load image: {image_path}")
            
            # 2. 创建标线mask对象
            lane_mask = LaneMask(image.shape[:2])
            
            # 添加标线段
            for name, mask in lane_masks.items():
                lane_type = lane_types.get(name, LaneType.SINGLE_SOLID)
                lane_mask.add_lane_segment(lane_type, mask)
            
            # 3. 检测遮挡
            self.logger.info("Detecting occlusions...")
            occlusion_analyses = self.occlusion_detector.detect_occlusions(
                lane_mask, occlusion_masks, occlusion_types
            )
            
            # 过滤显著遮挡
            significant_occlusions = self.occlusion_detector.filter_significant_occlusions(
                occlusion_analyses
            )
            
            self.logger.info(f"Found {len(significant_occlusions)} significant occlusions")
            
            # 4. 创建上下文信息
            contexts = []
            for analysis in significant_occlusions:
                lane_segment = lane_mask.lane_segments[analysis.lane_id]
                context = self.occlusion_detector.create_occlusion_context(
                    lane_segment, analysis, lane_mask
                )
                contexts.append(context)
            
            # 5. 规则推理
            self.logger.info("Performing rule inference...")
            inference_results = self.rule_engine.infer_completions(
                lane_mask, significant_occlusions, contexts
            )
            
            self.logger.info(f"Generated {len(inference_results)} inference results")
            
            # 6. 结构补全
            self.logger.info("Completing structure...")
            completion_result = self.structure_completer.complete_structure(
                lane_mask, inference_results
            )
            
            # 7. 生成结果
            result = {
                'image_path': image_path,
                'original_image': image,
                'original_mask': completion_result.original_mask,
                'completed_mask': completion_result.completed_mask,
                'completion_regions': completion_result.completion_regions,
                'confidence_map': completion_result.confidence_map,
                'occlusion_analyses': significant_occlusions,
                'inference_results': inference_results,
                'statistics': completion_result.statistics,
                'report': self.structure_completer.generate_completion_report(completion_result)
            }
            
            self.logger.info("Processing completed successfully")
            return result
            
        except Exception as e:
            self.logger.error(f"Error processing image {image_path}: {e}")
            raise
    
    def process_batch(self, 
                     image_list: List[str],
                     output_dir: str) -> List[Dict]:
        """
        批量处理图像
        
        Args:
            image_list: 图像路径列表
            output_dir: 输出目录
            
        Returns:
            处理结果列表
        """
        results = []
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        for i, image_path in enumerate(image_list):
            self.logger.info(f"Processing batch {i+1}/{len(image_list)}: {image_path}")
            
            try:
                # 这里需要根据实际情况加载标线和遮挡数据
                # 示例中使用占位符
                lane_masks, lane_types, occlusion_masks, occlusion_types = \
                    self._load_image_data(image_path)
                
                result = self.process_image(
                    image_path, lane_masks, lane_types, 
                    occlusion_masks, occlusion_types
                )
                
                # 保存结果
                self._save_result(result, output_path / f"result_{i:04d}")
                results.append(result)
                
            except Exception as e:
                self.logger.error(f"Failed to process {image_path}: {e}")
                continue
        
        return results
    
    def _load_image_data(self, image_path: str) -> Tuple[Dict, Dict, Dict, Dict]:
        """
        加载图像相关的标线和遮挡数据
        
        Args:
            image_path: 图像路径
            
        Returns:
            (lane_masks, lane_types, occlusion_masks, occlusion_types)
        """
        # 这里应该根据实际的数据格式实现
        # 示例中返回空数据
        return {}, {}, {}, {}
    
    def _save_result(self, result: Dict, output_path: Path):
        """
        保存处理结果
        
        Args:
            result: 处理结果
            output_path: 输出路径（不含扩展名）
        """
        # 保存可视化结果
        vis_image = self.visualizer.create_comparison_view(
            result['original_image'],
            result['original_mask'],
            result['completed_mask'],
            result['completion_regions'],
            result['confidence_map']
        )
        cv2.imwrite(str(output_path) + "_visualization.jpg", vis_image)
        
        # 保存mask
        cv2.imwrite(str(output_path) + "_completed_mask.png", 
                   result['completed_mask'] * 255)
        cv2.imwrite(str(output_path) + "_completion_regions.png", 
                   result['completion_regions'] * 255)
        
        # 保存报告
        self.io_handler.save_json(result['report'], str(output_path) + "_report.json")
    
    def evaluate_performance(self, 
                           results: List[Dict],
                           ground_truth_masks: List[np.ndarray]) -> Dict:
        """
        评估系统性能
        
        Args:
            results: 处理结果列表
            ground_truth_masks: 真值mask列表
            
        Returns:
            评估结果
        """
        if len(results) != len(ground_truth_masks):
            raise ValueError("Results and ground truth lists must have same length")
        
        metrics = {
            'completeness': [],
            'accuracy': [],
            'iou': [],
            'pixel_accuracy': []
        }
        
        for result, gt_mask in zip(results, ground_truth_masks):
            completed_mask = result['completed_mask']
            
            # 计算IoU
            intersection = np.logical_and(completed_mask, gt_mask)
            union = np.logical_or(completed_mask, gt_mask)
            iou = np.sum(intersection) / np.sum(union) if np.sum(union) > 0 else 0
            
            # 计算像素准确率
            pixel_acc = np.sum(completed_mask == gt_mask) / gt_mask.size
            
            # 计算完整性（召回率）
            completeness = np.sum(intersection) / np.sum(gt_mask) if np.sum(gt_mask) > 0 else 0
            
            # 计算准确性（精确率）
            accuracy = np.sum(intersection) / np.sum(completed_mask) if np.sum(completed_mask) > 0 else 0
            
            metrics['iou'].append(iou)
            metrics['pixel_accuracy'].append(pixel_acc)
            metrics['completeness'].append(completeness)
            metrics['accuracy'].append(accuracy)
        
        # 计算平均值
        avg_metrics = {
            f'avg_{key}': np.mean(values) for key, values in metrics.items()
        }
        
        return {**metrics, **avg_metrics}


def main():
    """主函数示例"""
    # 初始化系统
    system = OcclusionRecoverySystem("configs/config.yaml")
    
    # 示例：处理单张图像
    # 这里需要根据实际数据格式调整
    image_path = "data/images/sample.jpg"
    
    # 示例数据（实际使用时需要从文件加载）
    lane_masks = {
        "lane_1": np.zeros((480, 640), dtype=np.uint8),  # 示例空mask
    }
    lane_types = {
        "lane_1": LaneType.SINGLE_SOLID
    }
    occlusion_masks = {
        "vehicle_1": np.zeros((480, 640), dtype=np.uint8),  # 示例空mask
    }
    occlusion_types = {
        "vehicle_1": OcclusionType.DYNAMIC
    }
    
    try:
        result = system.process_image(
            image_path, lane_masks, lane_types, 
            occlusion_masks, occlusion_types
        )
        
        print("Processing completed successfully!")
        print(f"Statistics: {result['statistics']}")
        print(f"Report: {result['report']}")
        
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
