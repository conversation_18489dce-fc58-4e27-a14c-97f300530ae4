"""
测试智能补全系统
基于用户专业理解的完整补全流程
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import cv2
import json
from src.core.intelligent_completion_detector import IntelligentCompletionDetector
from src.core.intelligent_completion_executor import IntelligentCompletionExecutor
from src.utils.mask_processor import MaskProcessor


def test_intelligent_completion_system():
    """测试智能补全系统"""
    print("=== 智能补全系统测试 ===")
    
    # 1. 加载真实数据
    print("1. 加载真实数据...")
    image_path = "data/images/DJI_20220915153843_0320.JPG"
    mask_path = "data/masks/DJI_20220915153843_0320.png"
    
    try:
        image = cv2.imread(image_path)
        mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
        
        if image is None or mask is None:
            print("无法加载数据文件")
            return None
        
        # 缩放数据
        scale_factor = 0.25
        image = cv2.resize(image, None, fx=scale_factor, fy=scale_factor)
        mask = cv2.resize(mask, None, fx=scale_factor, fy=scale_factor, interpolation=cv2.INTER_NEAREST)
        
        print(f"   缩放后图像尺寸: {image.shape}")
        print(f"   Mask像素值: {np.unique(mask)}")
        
    except Exception as e:
        print(f"   加载数据时出错: {e}")
        return None
    
    # 2. 智能检测遮挡和触发条件
    print("2. 智能检测遮挡和触发条件...")
    detector = IntelligentCompletionDetector({
        'search_radius': 50,
        'same_type_threshold': 0.3,
        'trigger_confidence_threshold': 0.4
    })
    
    occlusion_masks, occlusion_types, completion_triggers = detector.detect_occlusions_and_triggers(mask)
    
    print(f"   检测到 {len(occlusion_masks)} 种遮挡物:")
    for name, occlusion_mask in occlusion_masks.items():
        pixels = np.sum(occlusion_mask)
        print(f"   - {name}: {occlusion_types[name].value} ({pixels} 像素)")
    
    print(f"   触发 {len(completion_triggers)} 个补全条件:")
    for i, trigger in enumerate(completion_triggers):
        print(f"   - 触发 {i+1}: {trigger.lane_type.value}, "
              f"置信度 {trigger.trigger_confidence:.3f}, "
              f"缺失区域 {np.sum(trigger.missing_area)} 像素")
    
    if len(completion_triggers) == 0:
        print("   没有触发补全条件")
        return None
    
    # 3. 解析标线数据
    print("3. 解析标线数据...")
    processor = MaskProcessor()
    lane_masks, lane_types, _, _ = processor.parse_grayscale_mask(mask)
    
    print(f"   检测到 {len(lane_masks)} 种标线类型")
    
    # 4. 执行智能补全
    print("4. 执行智能补全...")
    executor = IntelligentCompletionExecutor({
        'smoothing_kernel_size': 3,
        'line_width_estimation': True,
        'geometric_validation': True
    })
    
    completion_results = executor.execute_completions(completion_triggers, lane_masks, lane_types)
    
    print(f"   完成 {len(completion_results)} 个补全:")
    total_completed_pixels = 0
    for i, result in enumerate(completion_results):
        total_completed_pixels += result.completed_pixels
        print(f"   - 补全 {i+1}: {result.trigger.lane_type.value}, "
              f"{result.completed_pixels} 像素, "
              f"方法: {result.completion_method}, "
              f"置信度: {result.confidence:.3f}")
    
    # 5. 生成可视化
    print("5. 生成可视化...")
    output_dir = "examples/output_intelligent_completion"
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存原始数据
    cv2.imwrite(f"{output_dir}/original_image.jpg", image)
    cv2.imwrite(f"{output_dir}/original_mask.png", mask)
    
    # 保存遮挡物
    for name, occlusion_mask in occlusion_masks.items():
        cv2.imwrite(f"{output_dir}/occlusion_{name}.png", occlusion_mask * 255)
    
    # 保存触发区域
    for i, trigger in enumerate(completion_triggers):
        cv2.imwrite(f"{output_dir}/trigger_{i}_{trigger.lane_type.value}_missing.png", 
                   trigger.missing_area * 255)
        cv2.imwrite(f"{output_dir}/trigger_{i}_{trigger.lane_type.value}_occlusion.png", 
                   trigger.occlusion_area * 255)
    
    # 保存补全结果
    for i, result in enumerate(completion_results):
        cv2.imwrite(f"{output_dir}/completion_{i}_{result.completion_method}.png", 
                   result.completed_mask * 255)
    
    # 创建综合可视化
    comprehensive_vis = create_intelligent_completion_visualization(
        image, mask, occlusion_masks, completion_triggers, completion_results
    )
    cv2.imwrite(f"{output_dir}/intelligent_completion_visualization.jpg", comprehensive_vis)
    
    # 创建前后对比
    before_after = create_intelligent_before_after_comparison(
        image, mask, completion_results
    )
    cv2.imwrite(f"{output_dir}/intelligent_before_after.jpg", before_after)
    
    # 6. 生成详细报告
    print("6. 生成详细报告...")
    report = {
        'system_info': {
            'detection_method': 'intelligent_trigger_based',
            'completion_method': 'geometric_shape_based',
            'trigger_logic': 'surrounding_same_type_lanes'
        },
        'detection_results': {
            'occlusion_count': len(occlusion_masks),
            'trigger_count': len(completion_triggers),
            'completion_count': len(completion_results),
            'total_completed_pixels': total_completed_pixels
        },
        'occlusion_analysis': [
            {
                'name': name,
                'type': occlusion_types[name].value,
                'pixels': int(np.sum(mask))
            }
            for name, mask in occlusion_masks.items()
        ],
        'trigger_analysis': [
            {
                'trigger_id': i,
                'lane_type': trigger.lane_type.value,
                'confidence': trigger.trigger_confidence,
                'missing_pixels': int(np.sum(trigger.missing_area)),
                'occlusion_pixels': int(np.sum(trigger.occlusion_area)),
                'surrounding_lanes': len(trigger.surrounding_lanes)
            }
            for i, trigger in enumerate(completion_triggers)
        ],
        'completion_results': [
            {
                'completion_id': i,
                'lane_type': result.trigger.lane_type.value,
                'method': result.completion_method,
                'completed_pixels': result.completed_pixels,
                'confidence': result.confidence,
                'trigger_confidence': result.trigger.trigger_confidence
            }
            for i, result in enumerate(completion_results)
        ],
        'performance_metrics': {
            'trigger_success_rate': len(completion_results) / max(len(completion_triggers), 1),
            'average_completion_pixels': total_completed_pixels / max(len(completion_results), 1),
            'total_improvement_pixels': total_completed_pixels
        }
    }
    
    with open(f"{output_dir}/intelligent_completion_report.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False, default=str)
    
    # 7. 显示总结
    print("\n=== 智能补全系统测试总结 ===")
    print(f"🎯 检测结果:")
    print(f"  - 遮挡物类型: {len(occlusion_masks)}")
    print(f"  - 触发条件: {len(completion_triggers)}")
    print(f"  - 成功补全: {len(completion_results)}")
    
    print(f"📊 补全效果:")
    print(f"  - 总补全像素: {total_completed_pixels:,}")
    print(f"  - 平均每次补全: {total_completed_pixels // max(len(completion_results), 1):,} 像素")
    print(f"  - 触发成功率: {len(completion_results) / max(len(completion_triggers), 1) * 100:.1f}%")
    
    print(f"✅ 补全详情:")
    for i, result in enumerate(completion_results):
        print(f"  {i+1}. {result.trigger.lane_type.value}: {result.completed_pixels:,} 像素 "
              f"({result.completion_method}, 置信度: {result.confidence:.3f})")
    
    print(f"\n🎉 智能补全系统测试完成！")
    print(f"📁 结果已保存到: {output_dir}")
    
    return report


def create_intelligent_completion_visualization(image, mask, occlusion_masks, 
                                              triggers, completion_results):
    """创建智能补全可视化"""
    h, w = image.shape[:2]
    
    # 创建2x3布局
    result = np.zeros((h * 2, w * 3, 3), dtype=np.uint8)
    
    # 第一行：原图、原始标线、遮挡物
    result[:h, :w] = image
    
    # 原始标线（白色）
    lane_vis = np.zeros((h, w, 3), dtype=np.uint8)
    lane_vis[mask > 0] = [255, 255, 255]
    result[:h, w:w*2] = lane_vis
    
    # 遮挡物（不同颜色）
    occlusion_vis = np.zeros((h, w, 3), dtype=np.uint8)
    colors = {'car': [255, 0, 0], 'human': [0, 255, 0], 'clatter': [0, 0, 255], 'obstacle': [255, 255, 0]}
    for name, occlusion_mask in occlusion_masks.items():
        color = colors.get(name, [128, 128, 128])
        occlusion_vis[occlusion_mask > 0] = color
    result[:h, w*2:] = occlusion_vis
    
    # 第二行：触发区域、缺失区域、补全结果
    # 触发区域
    trigger_vis = np.zeros((h, w, 3), dtype=np.uint8)
    for i, trigger in enumerate(triggers):
        color = [(i * 60) % 255, (i * 120) % 255, (i * 180) % 255]
        trigger_vis[trigger.occlusion_area > 0] = color
    result[h:, :w] = trigger_vis
    
    # 缺失区域
    missing_vis = np.zeros((h, w, 3), dtype=np.uint8)
    for i, trigger in enumerate(triggers):
        color = [255, 255, 0]  # 黄色表示缺失
        missing_vis[trigger.missing_area > 0] = color
    result[h:, w:w*2] = missing_vis
    
    # 补全结果
    completion_vis = lane_vis.copy()
    for i, comp_result in enumerate(completion_results):
        completion_vis[comp_result.completed_mask > 0] = [0, 255, 0]  # 绿色补全
    result[h:, w*2:] = completion_vis
    
    # 添加标题
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(result, "Original Image", (10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Lane Markings", (w + 10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Occlusions", (w*2 + 10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Trigger Areas", (10, h + 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Missing Areas", (w + 10, h + 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Completed", (w*2 + 10, h + 25), font, 0.7, (255, 255, 255), 2)
    
    return result


def create_intelligent_before_after_comparison(image, original_mask, completion_results):
    """创建智能补全前后对比"""
    h, w = image.shape[:2]
    result = np.zeros((h, w * 2, 3), dtype=np.uint8)
    
    # 左侧：原始
    left_vis = image.copy()
    overlay_left = np.zeros_like(image)
    overlay_left[original_mask > 0] = [255, 255, 255]
    
    # 右侧：补全后
    right_vis = image.copy()
    overlay_right = np.zeros_like(image)
    overlay_right[original_mask > 0] = [255, 255, 255]
    
    # 添加补全结果（绿色）
    for comp_result in completion_results:
        overlay_right[comp_result.completed_mask > 0] = [0, 255, 0]
    
    # 混合图像
    alpha = 0.6
    left_vis = cv2.addWeighted(left_vis, 1-alpha, overlay_left, alpha, 0)
    right_vis = cv2.addWeighted(right_vis, 1-alpha, overlay_right, alpha, 0)
    
    result[:, :w] = left_vis
    result[:, w:] = right_vis
    
    # 添加标题和统计信息
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(result, "Before Intelligent Completion", (10, 30), font, 1.0, (255, 255, 255), 2)
    cv2.putText(result, "After Intelligent Completion", (w + 10, 30), font, 1.0, (255, 255, 255), 2)
    
    # 添加统计信息
    total_completed = sum(np.sum(comp.completed_mask) for comp in completion_results)
    cv2.putText(result, f"Original Pixels: {np.sum(original_mask > 0):,}", 
                (10, h - 60), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, f"Added Pixels: {total_completed:,}", 
                (w + 10, h - 60), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, f"Completions: {len(completion_results)}", 
                (w + 10, h - 30), font, 0.7, (255, 255, 255), 2)
    
    return result


if __name__ == "__main__":
    try:
        report = test_intelligent_completion_system()
        if report is not None:
            print("\n🎉 智能补全系统测试运行成功！")
        
    except Exception as e:
        print(f"智能补全系统测试运行出错: {e}")
        import traceback
        traceback.print_exc()
