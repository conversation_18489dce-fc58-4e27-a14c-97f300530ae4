"""
规则数据模型
定义推理规则的数据结构
"""

from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
from .mask_model import LaneType, LaneSegment, OcclusionRegion


class RuleType(Enum):
    """规则类型枚举"""
    LINE_EXTENSION = "line_extension"        # 线段延伸
    SYMMETRY_COMPLETION = "symmetry_completion"  # 对称补全
    PATTERN_REPETITION = "pattern_repetition"    # 模式重复
    INTERSECTION_INFERENCE = "intersection_inference"  # 交叉口推理
    CONTEXTUAL_COMPLETION = "contextual_completion"    # 上下文补全
    PERPENDICULAR_COMPLETION = "perpendicular_completion"  # 垂直补全
    PARALLEL_COMPLETION = "parallel_completion"  # 平行补全
    AREA_FILL = "area_fill"  # 区域填充
    SHAPE_COMPLETION = "shape_completion"  # 形状补全


class ConditionType(Enum):
    """条件类型枚举"""
    LANE_TYPE = "lane_type"              # 标线类型
    OCCLUSION_RATIO = "occlusion_ratio"  # 遮挡比例
    VISIBLE_LENGTH = "visible_length"    # 可见长度
    DIRECTION = "direction"              # 方向
    PROXIMITY = "proximity"              # 邻近性
    GEOMETRIC = "geometric"              # 几何特征


@dataclass
class RuleCondition:
    """规则条件"""
    condition_type: ConditionType
    operator: str  # "==", "!=", ">", "<", ">=", "<=", "in", "not_in"
    value: Any
    weight: float = 1.0

    def evaluate(self, context: Dict[str, Any]) -> Tuple[bool, float]:
        """评估条件是否满足"""
        if self.condition_type.value not in context:
            return False, 0.0

        context_value = context[self.condition_type.value]

        try:
            if self.operator == "==":
                result = context_value == self.value
            elif self.operator == "!=":
                result = context_value != self.value
            elif self.operator == ">":
                result = context_value > self.value
            elif self.operator == "<":
                result = context_value < self.value
            elif self.operator == ">=":
                result = context_value >= self.value
            elif self.operator == "<=":
                result = context_value <= self.value
            elif self.operator == "in":
                result = context_value in self.value
            elif self.operator == "not_in":
                result = context_value not in self.value
            else:
                return False, 0.0

            confidence = self.weight if result else 0.0
            return result, confidence

        except (TypeError, ValueError):
            return False, 0.0


@dataclass
class CompletionAction:
    """补全动作"""
    action_type: str  # "extend", "interpolate", "mirror", "pattern_fill"
    parameters: Dict[str, Any] = field(default_factory=dict)

    def execute(self,
                lane_segment: LaneSegment,
                occlusion_region: OcclusionRegion,
                context: Dict[str, Any]) -> np.ndarray:
        """执行补全动作"""
        if self.action_type == "extend":
            return self._extend_line(lane_segment, occlusion_region, context)
        elif self.action_type == "interpolate":
            return self._interpolate_gap(lane_segment, occlusion_region, context)
        elif self.action_type == "mirror":
            return self._mirror_symmetry(lane_segment, occlusion_region, context)
        elif self.action_type == "pattern_fill":
            return self._pattern_fill(lane_segment, occlusion_region, context)
        else:
            raise ValueError(f"Unknown action type: {self.action_type}")

    def _extend_line(self,
                    lane_segment: LaneSegment,
                    occlusion_region: OcclusionRegion,
                    context: Dict[str, Any]) -> np.ndarray:
        """线段延伸"""
        import cv2

        # 获取标线方向
        direction = lane_segment.get_direction_vector()
        if direction is None:
            return np.zeros_like(occlusion_region.mask)

        # 获取延伸参数
        line_width = self.parameters.get("line_width", 3)

        # 创建结果mask
        result_mask = np.zeros_like(occlusion_region.mask)

        # 获取可见的标线部分（排除遮挡区域）
        visible_lane = np.logical_and(lane_segment.mask, ~occlusion_region.mask)

        if not np.any(visible_lane):
            return result_mask

        # 获取可见标线的骨架
        from skimage.morphology import skeletonize
        skeleton = skeletonize(visible_lane)

        if not np.any(skeleton):
            return result_mask

        # 找到骨架的端点
        kernel = np.ones((3, 3), np.uint8)
        neighbors = cv2.filter2D(skeleton.astype(np.uint8), -1, kernel)
        endpoints = np.where((skeleton > 0) & (neighbors == 2))

        if len(endpoints[0]) == 0:
            return result_mask

        # 对每个端点进行延伸
        for i in range(len(endpoints[0])):
            y, x = endpoints[0][i], endpoints[1][i]

            # 计算局部方向
            local_direction = self._get_local_direction(skeleton, x, y)
            if local_direction is None:
                continue

            # 在遮挡区域内延伸
            self._extend_from_point(result_mask, x, y, local_direction,
                                  line_width, occlusion_region.mask)

        return result_mask

    def _get_local_direction(self, skeleton, x, y):
        """获取点的局部方向"""
        # 在点周围寻找连接的骨架点
        window_size = 10
        y_min = max(0, y - window_size)
        y_max = min(skeleton.shape[0], y + window_size + 1)
        x_min = max(0, x - window_size)
        x_max = min(skeleton.shape[1], x + window_size + 1)

        # 找到附近的骨架点
        local_skeleton = skeleton[y_min:y_max, x_min:x_max]
        local_points = np.where(local_skeleton)

        if len(local_points[0]) < 2:
            return None

        # 转换为全局坐标
        global_y = local_points[0] + y_min
        global_x = local_points[1] + x_min

        # 计算主方向
        points = np.column_stack((global_x, global_y))
        if len(points) < 2:
            return None

        # 使用PCA计算主方向
        mean_point = np.mean(points, axis=0)
        centered_points = points - mean_point

        if len(centered_points) < 2:
            return None

        cov_matrix = np.cov(centered_points.T)
        eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)

        # 主方向
        main_direction = eigenvectors[:, np.argmax(eigenvalues)]

        # 确定延伸方向（远离现有标线）
        center_point = np.array([x, y])
        direction_point = center_point + main_direction * 10

        # 检查哪个方向远离现有标线
        opposite_direction = center_point - main_direction * 10

        return main_direction

    def _extend_from_point(self, result_mask, start_x, start_y, direction, width, occlusion_mask):
        """从点开始延伸"""
        import cv2

        # 归一化方向向量
        direction = direction / (np.linalg.norm(direction) + 1e-8)

        # 延伸长度
        max_length = 100

        for length in range(1, max_length):
            # 计算延伸点
            end_x = int(start_x + direction[0] * length)
            end_y = int(start_y + direction[1] * length)

            # 检查是否超出边界
            if (end_x < 0 or end_x >= result_mask.shape[1] or
                end_y < 0 or end_y >= result_mask.shape[0]):
                break

            # 检查是否还在遮挡区域内
            if not occlusion_mask[end_y, end_x]:
                break

            # 画线
            cv2.line(result_mask, (start_x, start_y), (end_x, end_y), 1, width)

    def _interpolate_gap(self,
                        lane_segment: LaneSegment,
                        occlusion_region: OcclusionRegion,
                        context: Dict[str, Any]) -> np.ndarray:
        """间隙插值"""
        # 实现间隙插值算法
        return np.zeros_like(occlusion_region.mask)

    def _mirror_symmetry(self,
                        lane_segment: LaneSegment,
                        occlusion_region: OcclusionRegion,
                        context: Dict[str, Any]) -> np.ndarray:
        """对称镜像"""
        import cv2

        # 创建结果mask
        result_mask = np.zeros_like(occlusion_region.mask)

        # 获取可见的标线部分
        visible_lane = np.logical_and(lane_segment.mask, ~occlusion_region.mask)

        if not np.any(visible_lane):
            return result_mask

        # 获取标线的边界框
        y_coords, x_coords = np.where(visible_lane)
        if len(x_coords) == 0:
            return result_mask

        # 计算标线的中心
        center_x = int(np.mean(x_coords))
        center_y = int(np.mean(y_coords))

        # 获取对称轴参数
        symmetry_axis = self.parameters.get("symmetry_axis", "auto")

        if symmetry_axis == "vertical":
            # 垂直对称（适用于直行箭头）
            result_mask = self._vertical_mirror(visible_lane, occlusion_region.mask, center_x)
        elif symmetry_axis == "horizontal":
            # 水平对称
            result_mask = self._horizontal_mirror(visible_lane, occlusion_region.mask, center_y)
        else:
            # 自动检测对称轴
            # 尝试垂直对称
            vertical_result = self._vertical_mirror(visible_lane, occlusion_region.mask, center_x)
            if np.sum(vertical_result) > 0:
                result_mask = vertical_result
            else:
                # 尝试水平对称
                result_mask = self._horizontal_mirror(visible_lane, occlusion_region.mask, center_y)

        return result_mask

    def _vertical_mirror(self, visible_lane, occlusion_mask, center_x):
        """垂直镜像"""
        result_mask = np.zeros_like(visible_lane)

        # 对每个可见像素进行镜像
        y_coords, x_coords = np.where(visible_lane)

        for y, x in zip(y_coords, x_coords):
            # 计算镜像点
            mirror_x = 2 * center_x - x

            # 检查镜像点是否在有效范围内且在遮挡区域内
            if (0 <= mirror_x < visible_lane.shape[1] and
                0 <= y < visible_lane.shape[0] and
                occlusion_mask[y, mirror_x]):
                result_mask[y, mirror_x] = 1

        return result_mask

    def _horizontal_mirror(self, visible_lane, occlusion_mask, center_y):
        """水平镜像"""
        result_mask = np.zeros_like(visible_lane)

        # 对每个可见像素进行镜像
        y_coords, x_coords = np.where(visible_lane)

        for y, x in zip(y_coords, x_coords):
            # 计算镜像点
            mirror_y = 2 * center_y - y

            # 检查镜像点是否在有效范围内且在遮挡区域内
            if (0 <= x < visible_lane.shape[1] and
                0 <= mirror_y < visible_lane.shape[0] and
                occlusion_mask[mirror_y, x]):
                result_mask[mirror_y, x] = 1

        return result_mask

    def _pattern_fill(self,
                     lane_segment: LaneSegment,
                     occlusion_region: OcclusionRegion,
                     context: Dict[str, Any]) -> np.ndarray:
        """模式填充"""
        import cv2

        # 创建结果mask
        result_mask = np.zeros_like(occlusion_region.mask)

        # 获取模式类型
        pattern_type = self.parameters.get("pattern_type", "dashed")

        if pattern_type == "dashed":
            result_mask = self._fill_dashed_pattern(lane_segment, occlusion_region)
        elif pattern_type == "diagonal_lines":
            result_mask = self._fill_diagonal_pattern(lane_segment, occlusion_region)
        elif pattern_type == "grid":
            result_mask = self._fill_grid_pattern(lane_segment, occlusion_region)

        return result_mask

    def _fill_dashed_pattern(self, lane_segment, occlusion_region):
        """填充虚线模式"""
        import cv2

        result_mask = np.zeros_like(occlusion_region.mask)

        # 简化算法：在遮挡区域内创建虚线模式
        occlusion_coords = np.where(occlusion_region.mask)
        if len(occlusion_coords[0]) == 0:
            return result_mask

        # 获取遮挡区域的边界
        y_min, y_max = np.min(occlusion_coords[0]), np.max(occlusion_coords[0])
        x_min, x_max = np.min(occlusion_coords[1]), np.max(occlusion_coords[1])

        # 计算中心Y坐标
        center_y = (y_min + y_max) // 2

        # 虚线参数
        dash_length = 10
        gap_length = 10
        line_width = 2

        # 沿中心线绘制虚线
        current_x = x_min
        is_dash = True

        while current_x < x_max:
            if is_dash:
                end_x = min(current_x + dash_length, x_max)

                # 绘制虚线段
                if (center_y >= 0 and center_y < result_mask.shape[0] and
                    current_x >= 0 and current_x < result_mask.shape[1] and
                    end_x >= 0 and end_x < result_mask.shape[1] and
                    current_x != end_x):  # 确保起点和终点不同

                    # 创建临时线段
                    temp_mask = np.zeros_like(result_mask)
                    cv2.line(temp_mask, (int(current_x), int(center_y)), (int(end_x), int(center_y)), 1, line_width)

                    # 只在遮挡区域内且不与现有标线重叠的地方添加
                    valid_area = np.logical_and(temp_mask, occlusion_region.mask)
                    valid_area = np.logical_and(valid_area, ~lane_segment.mask)

                    result_mask = np.logical_or(result_mask, valid_area)

                current_x = end_x
            else:
                current_x += gap_length

            is_dash = not is_dash

        return result_mask.astype(np.uint8)

    def _fill_diagonal_pattern(self, lane_segment, occlusion_region):
        """填充斜线模式（导流区）"""
        import cv2

        result_mask = np.zeros_like(occlusion_region.mask)

        # 获取遮挡区域的边界
        occlusion_coords = np.where(occlusion_region.mask)
        if len(occlusion_coords[0]) == 0:
            return result_mask

        y_min = int(np.min(occlusion_coords[0]))
        y_max = int(np.max(occlusion_coords[0]))
        x_min = int(np.min(occlusion_coords[1]))
        x_max = int(np.max(occlusion_coords[1]))

        # 绘制斜线
        line_spacing = 15  # 斜线间距
        line_width = 2

        for i in range(0, x_max - x_min + y_max - y_min, line_spacing):
            # 计算斜线的起点和终点
            start_x = x_min + i
            start_y = y_min
            end_x = x_min
            end_y = y_min + i

            # 确保在边界内
            if start_x > x_max:
                start_x = x_max
                start_y = y_min + (i - (x_max - x_min))

            if end_y > y_max:
                end_y = y_max
                end_x = x_min + (i - (y_max - y_min))

            # 只在遮挡区域内绘制
            if (start_y < result_mask.shape[0] and start_x < result_mask.shape[1] and
                end_y < result_mask.shape[0] and end_x < result_mask.shape[1]):

                # 创建临时线段
                temp_mask = np.zeros_like(result_mask)
                cv2.line(temp_mask, (start_x, start_y), (end_x, end_y), 1, line_width)

                # 只保留在遮挡区域内的部分
                result_mask = np.logical_or(result_mask,
                                          np.logical_and(temp_mask, occlusion_region.mask))

        return result_mask.astype(np.uint8)

    def _fill_grid_pattern(self, lane_segment, occlusion_region):
        """填充网格模式"""
        import cv2

        result_mask = np.zeros_like(occlusion_region.mask)

        # 获取遮挡区域的边界
        occlusion_coords = np.where(occlusion_region.mask)
        if len(occlusion_coords[0]) == 0:
            return result_mask

        y_min = int(np.min(occlusion_coords[0]))
        y_max = int(np.max(occlusion_coords[0]))
        x_min = int(np.min(occlusion_coords[1]))
        x_max = int(np.max(occlusion_coords[1]))

        # 网格参数
        grid_spacing = 20
        line_width = 2

        # 绘制水平线
        for y in range(y_min, y_max, grid_spacing):
            if y < result_mask.shape[0]:
                temp_mask = np.zeros_like(result_mask)
                cv2.line(temp_mask, (x_min, y), (x_max, y), 1, line_width)
                result_mask = np.logical_or(result_mask,
                                          np.logical_and(temp_mask, occlusion_region.mask))

        # 绘制垂直线
        for x in range(x_min, x_max, grid_spacing):
            if x < result_mask.shape[1]:
                temp_mask = np.zeros_like(result_mask)
                cv2.line(temp_mask, (x, y_min), (x, y_max), 1, line_width)
                result_mask = np.logical_or(result_mask,
                                          np.logical_and(temp_mask, occlusion_region.mask))

        return result_mask.astype(np.uint8)


@dataclass
class InferenceRule:
    """推理规则"""
    rule_id: str
    rule_type: RuleType
    name: str
    description: str
    conditions: List[RuleCondition]
    actions: List[CompletionAction]
    priority: int = 1
    confidence_threshold: float = 0.5

    def match(self, context: Dict[str, Any]) -> Tuple[bool, float]:
        """匹配规则条件"""
        total_weight = sum(condition.weight for condition in self.conditions)
        if total_weight == 0:
            return False, 0.0

        satisfied_weight = 0.0
        all_satisfied = True

        for condition in self.conditions:
            satisfied, confidence = condition.evaluate(context)
            if satisfied:
                satisfied_weight += confidence
            else:
                all_satisfied = False

        # 计算总体置信度
        overall_confidence = satisfied_weight / total_weight

        # 规则匹配需要所有条件都满足，且总体置信度超过阈值
        is_matched = all_satisfied and overall_confidence >= self.confidence_threshold

        return is_matched, overall_confidence

    def apply(self,
             lane_segment: LaneSegment,
             occlusion_region: OcclusionRegion,
             context: Dict[str, Any]) -> Tuple[np.ndarray, float]:
        """应用规则进行补全"""
        is_matched, confidence = self.match(context)

        if not is_matched:
            return np.zeros_like(occlusion_region.mask), 0.0

        # 执行所有动作并合并结果
        result_mask = np.zeros_like(occlusion_region.mask)

        for action in self.actions:
            action_result = action.execute(lane_segment, occlusion_region, context)
            result_mask = np.logical_or(result_mask, action_result)

        return result_mask.astype(np.uint8), confidence


@dataclass
class RuleSet:
    """规则集合"""
    rules: List[InferenceRule] = field(default_factory=list)

    def add_rule(self, rule: InferenceRule):
        """添加规则"""
        self.rules.append(rule)
        # 按优先级排序
        self.rules.sort(key=lambda r: r.priority, reverse=True)

    def find_matching_rules(self, context: Dict[str, Any]) -> List[Tuple[InferenceRule, float]]:
        """查找匹配的规则"""
        matching_rules = []

        for rule in self.rules:
            is_matched, confidence = rule.match(context)
            if is_matched:
                matching_rules.append((rule, confidence))

        # 按置信度排序
        matching_rules.sort(key=lambda x: x[1], reverse=True)
        return matching_rules

    def get_best_rule(self, context: Dict[str, Any]) -> Optional[Tuple[InferenceRule, float]]:
        """获取最佳匹配规则"""
        matching_rules = self.find_matching_rules(context)
        return matching_rules[0] if matching_rules else None


def create_default_rules() -> RuleSet:
    """创建默认规则集"""
    rule_set = RuleSet()

    # 规则1: 单实线延伸
    solid_line_extension = InferenceRule(
        rule_id="solid_line_ext_001",
        rule_type=RuleType.LINE_EXTENSION,
        name="单实线延伸",
        description="当单实线被部分遮挡时，沿原方向延伸",
        conditions=[
            RuleCondition(ConditionType.LANE_TYPE, "==", LaneType.SOLID_LINE, 1.0),
            RuleCondition(ConditionType.OCCLUSION_RATIO, "<", 0.8, 0.8),
            RuleCondition(ConditionType.VISIBLE_LENGTH, ">", 10, 0.6)  # 降低要求
        ],
        actions=[
            CompletionAction("extend", {"extension_length": 100, "line_width": 3})
        ],
        priority=5,
        confidence_threshold=0.3  # 降低置信度阈值
    )

    # 规则2: 虚线模式重复
    dashed_line_pattern = InferenceRule(
        rule_id="dashed_line_pattern_001",
        rule_type=RuleType.PATTERN_REPETITION,
        name="虚线模式重复",
        description="根据可见虚线段推断遮挡区域的虚线模式",
        conditions=[
            RuleCondition(ConditionType.LANE_TYPE, "==", LaneType.DASHED_LINE, 1.0),
            RuleCondition(ConditionType.VISIBLE_LENGTH, ">", 10, 0.7)  # 降低要求
        ],
        actions=[
            CompletionAction("pattern_fill", {"pattern_type": "dashed"})
        ],
        priority=4,
        confidence_threshold=0.2  # 降低置信度阈值
    )

    # 规则3: 双实线延伸
    double_line_extension = InferenceRule(
        rule_id="double_line_ext_001",
        rule_type=RuleType.LINE_EXTENSION,
        name="双实线延伸",
        description="当双实线被部分遮挡时，沿原方向延伸",
        conditions=[
            RuleCondition(ConditionType.LANE_TYPE, "==", LaneType.DOUBLE_LINE, 1.0),
            RuleCondition(ConditionType.OCCLUSION_RATIO, "<", 0.8, 0.8),
            RuleCondition(ConditionType.VISIBLE_LENGTH, ">", 25, 0.7)
        ],
        actions=[
            CompletionAction("extend", {"extension_length": 100, "line_width": 5})
        ],
        priority=5,
        confidence_threshold=0.7
    )

    # 规则4: 停止线补全
    stop_line_completion = InferenceRule(
        rule_id="stop_line_comp_001",
        rule_type=RuleType.LINE_EXTENSION,
        name="停止线补全",
        description="停止线的横向补全",
        conditions=[
            RuleCondition(ConditionType.LANE_TYPE, "==", LaneType.STOP_LINE, 1.0),
            RuleCondition(ConditionType.OCCLUSION_RATIO, "<", 0.7, 0.8)
        ],
        actions=[
            CompletionAction("extend", {"extension_length": 50, "line_width": 8})
        ],
        priority=6,
        confidence_threshold=0.6
    )

    # 规则5: 直行箭头对称补全
    straight_arrow_symmetry = InferenceRule(
        rule_id="straight_arrow_sym_001",
        rule_type=RuleType.SYMMETRY_COMPLETION,
        name="直行箭头对称补全",
        description="基于直行箭头的对称性补全缺失部分",
        conditions=[
            RuleCondition(ConditionType.LANE_TYPE, "==", LaneType.ARROW_STRAIGHT, 1.0),
            RuleCondition(ConditionType.OCCLUSION_RATIO, "<", 1.0, 0.8)  # 放宽条件
        ],
        actions=[
            CompletionAction("mirror", {"symmetry_axis": "vertical"})
        ],
        priority=7,
        confidence_threshold=0.5  # 降低置信度阈值
    )

    # 规则6: 左转箭头对称补全
    left_arrow_symmetry = InferenceRule(
        rule_id="left_arrow_sym_001",
        rule_type=RuleType.SYMMETRY_COMPLETION,
        name="左转箭头对称补全",
        description="基于左转箭头的对称性补全缺失部分",
        conditions=[
            RuleCondition(ConditionType.LANE_TYPE, "==", LaneType.ARROW_LEFT, 1.0),
            RuleCondition(ConditionType.OCCLUSION_RATIO, "<", 0.6, 0.8)
        ],
        actions=[
            CompletionAction("mirror", {"symmetry_axis": "auto"})
        ],
        priority=7,
        confidence_threshold=0.7
    )

    # 规则7: 右转箭头对称补全
    right_arrow_symmetry = InferenceRule(
        rule_id="right_arrow_sym_001",
        rule_type=RuleType.SYMMETRY_COMPLETION,
        name="右转箭头对称补全",
        description="基于右转箭头的对称性补全缺失部分",
        conditions=[
            RuleCondition(ConditionType.LANE_TYPE, "==", LaneType.ARROW_RIGHT, 1.0),
            RuleCondition(ConditionType.OCCLUSION_RATIO, "<", 0.6, 0.8)
        ],
        actions=[
            CompletionAction("mirror", {"symmetry_axis": "auto"})
        ],
        priority=7,
        confidence_threshold=0.7
    )

    # 规则8: 导流区补全
    diverge_area_completion = InferenceRule(
        rule_id="diverge_area_comp_001",
        rule_type=RuleType.PATTERN_REPETITION,
        name="导流区补全",
        description="导流区的斜线模式补全",
        conditions=[
            RuleCondition(ConditionType.LANE_TYPE, "==", LaneType.DIVERGE_AREA, 1.0),
            RuleCondition(ConditionType.OCCLUSION_RATIO, "<", 0.7, 0.7)
        ],
        actions=[
            CompletionAction("pattern_fill", {"pattern_type": "diagonal_lines"})
        ],
        priority=4,
        confidence_threshold=0.6
    )

    # 规则9: 网格区补全
    grid_area_completion = InferenceRule(
        rule_id="grid_area_comp_001",
        rule_type=RuleType.PATTERN_REPETITION,
        name="网格区补全",
        description="网格区的网格模式补全",
        conditions=[
            RuleCondition(ConditionType.LANE_TYPE, "==", LaneType.GRID_AREA, 1.0),
            RuleCondition(ConditionType.OCCLUSION_RATIO, "<", 0.8, 0.7)
        ],
        actions=[
            CompletionAction("pattern_fill", {"pattern_type": "grid"})
        ],
        priority=4,
        confidence_threshold=0.5
    )

    # 添加所有规则
    rule_set.add_rule(solid_line_extension)
    rule_set.add_rule(dashed_line_pattern)
    rule_set.add_rule(double_line_extension)
    rule_set.add_rule(stop_line_completion)
    rule_set.add_rule(straight_arrow_symmetry)
    rule_set.add_rule(left_arrow_symmetry)
    rule_set.add_rule(right_arrow_symmetry)
    rule_set.add_rule(diverge_area_completion)
    rule_set.add_rule(grid_area_completion)

    return rule_set
