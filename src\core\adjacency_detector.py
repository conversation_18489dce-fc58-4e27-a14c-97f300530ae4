"""
邻接遮挡检测器
检测标线与遮挡物的贴合关系，而非重合关系
"""

import numpy as np
import cv2
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass
import logging

from ..models.mask_model import (
    LaneMask, LaneSegment, OcclusionRegion, OcclusionType,
    OCCLUSION_PIXELS, OCCLUSION_TYPE_MAP
)


@dataclass
class AdjacencyAnalysis:
    """邻接分析结果"""
    lane_id: int
    occlusion_id: int
    occlusion_type: OcclusionType
    adjacency_ratio: float  # 邻接比例
    adjacency_length: float  # 邻接长度
    confidence: float
    contact_points: List[Tuple[int, int]]  # 接触点
    gap_distance: float  # 平均间隙距离


class AdjacencyDetector:
    """邻接遮挡检测器"""
    
    def __init__(self, config: Dict = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.max_gap_distance = self.config.get('max_gap_distance', 10)  # 最大间隙距离
        self.min_adjacency_length = self.config.get('min_adjacency_length', 20)  # 最小邻接长度
        self.confidence_threshold = self.config.get('confidence_threshold', 0.3)
        self.dilation_kernel_size = self.config.get('dilation_kernel_size', 5)  # 膨胀核大小
    
    def detect_occlusions_from_mask(self, grayscale_mask: np.ndarray) -> Tuple[Dict, Dict]:
        """
        从灰度mask中检测遮挡物
        
        Args:
            grayscale_mask: 灰度mask图像
            
        Returns:
            (occlusion_masks, occlusion_types)
        """
        occlusion_masks = {}
        occlusion_types = {}
        
        # 检测每种遮挡物类型
        for pixel_value, occlusion_name in OCCLUSION_TYPE_MAP.items():
            occlusion_mask = (grayscale_mask == pixel_value).astype(np.uint8)
            
            if np.sum(occlusion_mask) > 0:
                # 根据遮挡物类型确定OcclusionType
                if occlusion_name in ['car', 'human']:
                    occ_type = OcclusionType.DYNAMIC
                else:  # clatter, obstacle
                    occ_type = OcclusionType.STATIC
                
                occlusion_masks[occlusion_name] = occlusion_mask
                occlusion_types[occlusion_name] = occ_type
                
                self.logger.info(f"检测到 {occlusion_name}: {np.sum(occlusion_mask)} 像素")
        
        return occlusion_masks, occlusion_types
    
    def detect_adjacencies(self, 
                          lane_mask: LaneMask,
                          occlusion_masks: Dict[str, np.ndarray],
                          occlusion_types: Dict[str, OcclusionType]) -> List[AdjacencyAnalysis]:
        """
        检测标线与遮挡物的邻接关系
        
        Args:
            lane_mask: 标线mask对象
            occlusion_masks: 遮挡物mask字典
            occlusion_types: 遮挡物类型字典
            
        Returns:
            邻接分析结果列表
        """
        adjacency_analyses = []
        
        # 为每个遮挡物创建遮挡区域
        for occlusion_name, occlusion_mask in occlusion_masks.items():
            occlusion_type = occlusion_types[occlusion_name]
            occlusion_id = lane_mask.add_occlusion_region(occlusion_type, occlusion_mask)
            
            # 检测与每个标线段的邻接关系
            for lane_id, lane_segment in lane_mask.lane_segments.items():
                analysis = self._analyze_adjacency(
                    lane_segment, 
                    lane_mask.occlusion_regions[occlusion_id],
                    occlusion_name
                )
                
                if analysis and analysis.confidence >= self.confidence_threshold:
                    adjacency_analyses.append(analysis)
                    self.logger.info(
                        f"检测到邻接: 标线 {lane_id} ({lane_segment.lane_type.value}) "
                        f"与 {occlusion_name} 邻接 {analysis.adjacency_ratio:.2%}, "
                        f"置信度: {analysis.confidence:.3f}"
                    )
        
        return adjacency_analyses
    
    def _analyze_adjacency(self, 
                          lane_segment: LaneSegment,
                          occlusion_region: OcclusionRegion,
                          occlusion_name: str) -> Optional[AdjacencyAnalysis]:
        """
        分析标线段与遮挡区域的邻接关系
        
        Args:
            lane_segment: 标线段
            occlusion_region: 遮挡区域
            occlusion_name: 遮挡物名称
            
        Returns:
            邻接分析结果
        """
        # 1. 检查是否有直接重合（如果有，说明分割有问题）
        overlap = np.logical_and(lane_segment.mask, occlusion_region.mask)
        if np.sum(overlap) > 10:  # 允许少量重合（分割误差）
            self.logger.warning(f"标线 {lane_segment.lane_id} 与 {occlusion_name} 有重合，可能存在分割问题")
        
        # 2. 计算邻接关系
        contact_points, gap_distance = self._find_contact_points(
            lane_segment.mask, occlusion_region.mask
        )
        
        if len(contact_points) == 0 or gap_distance > self.max_gap_distance:
            return None
        
        # 3. 计算邻接长度和比例
        adjacency_length = len(contact_points)
        
        # 计算标线的周长作为参考
        lane_perimeter = self._calculate_perimeter(lane_segment.mask)
        adjacency_ratio = adjacency_length / max(lane_perimeter, 1)
        
        # 4. 计算置信度
        confidence = self._calculate_adjacency_confidence(
            adjacency_length, gap_distance, adjacency_ratio
        )
        
        if adjacency_length < self.min_adjacency_length:
            return None
        
        return AdjacencyAnalysis(
            lane_id=lane_segment.lane_id,
            occlusion_id=occlusion_region.occlusion_id,
            occlusion_type=occlusion_region.occlusion_type,
            adjacency_ratio=adjacency_ratio,
            adjacency_length=adjacency_length,
            confidence=confidence,
            contact_points=contact_points,
            gap_distance=gap_distance
        )
    
    def _find_contact_points(self, 
                           lane_mask: np.ndarray, 
                           occlusion_mask: np.ndarray) -> Tuple[List[Tuple[int, int]], float]:
        """
        找到标线与遮挡物的接触点
        
        Args:
            lane_mask: 标线mask
            occlusion_mask: 遮挡物mask
            
        Returns:
            (接触点列表, 平均间隙距离)
        """
        # 1. 对标线mask进行膨胀，寻找邻近区域
        kernel = np.ones((self.dilation_kernel_size, self.dilation_kernel_size), np.uint8)
        dilated_lane = cv2.dilate(lane_mask, kernel, iterations=1)
        
        # 2. 找到膨胀后的标线与遮挡物的交集
        potential_contact = np.logical_and(dilated_lane, occlusion_mask)
        
        if not np.any(potential_contact):
            return [], float('inf')
        
        # 3. 对每个潜在接触点，计算到最近标线像素的距离
        contact_points = []
        distances = []
        
        potential_coords = np.where(potential_contact)
        lane_coords = np.where(lane_mask)
        
        if len(lane_coords[0]) == 0:
            return [], float('inf')
        
        # 构建标线坐标数组
        lane_points = np.column_stack((lane_coords[1], lane_coords[0]))  # (x, y)
        
        for i in range(len(potential_coords[0])):
            y, x = potential_coords[0][i], potential_coords[1][i]
            
            # 计算到所有标线点的距离
            distances_to_lane = np.sqrt(np.sum((lane_points - np.array([x, y]))**2, axis=1))
            min_distance = np.min(distances_to_lane)
            
            # 如果距离在合理范围内，认为是接触点
            if min_distance <= self.max_gap_distance:
                contact_points.append((x, y))
                distances.append(min_distance)
        
        avg_distance = np.mean(distances) if distances else float('inf')
        
        return contact_points, avg_distance
    
    def _calculate_perimeter(self, mask: np.ndarray) -> float:
        """计算mask的周长"""
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if len(contours) == 0:
            return 0.0
        
        total_perimeter = 0.0
        for contour in contours:
            total_perimeter += cv2.arcLength(contour, True)
        
        return total_perimeter
    
    def _calculate_adjacency_confidence(self, 
                                      adjacency_length: float,
                                      gap_distance: float,
                                      adjacency_ratio: float) -> float:
        """
        计算邻接置信度
        
        Args:
            adjacency_length: 邻接长度
            gap_distance: 间隙距离
            adjacency_ratio: 邻接比例
            
        Returns:
            置信度 [0, 1]
        """
        # 基于邻接长度的置信度
        length_confidence = min(adjacency_length / 100.0, 1.0)
        
        # 基于间隙距离的置信度（距离越小置信度越高）
        distance_confidence = max(0, 1.0 - gap_distance / self.max_gap_distance)
        
        # 基于邻接比例的置信度
        ratio_confidence = min(adjacency_ratio * 10, 1.0)  # 10%邻接比例 = 100%置信度
        
        # 综合置信度
        overall_confidence = (length_confidence * 0.4 + 
                            distance_confidence * 0.4 + 
                            ratio_confidence * 0.2)
        
        return overall_confidence
    
    def create_adjacency_context(self, 
                                lane_segment: LaneSegment,
                                adjacency_analysis: AdjacencyAnalysis,
                                lane_mask: LaneMask) -> Dict:
        """
        创建邻接上下文信息
        
        Args:
            lane_segment: 标线段
            adjacency_analysis: 邻接分析结果
            lane_mask: 标线mask对象
            
        Returns:
            上下文字典
        """
        # 计算可见长度（整个标线的长度，因为没有重合遮挡）
        visible_length = len(lane_segment.pixels)
        
        # 获取方向向量
        direction = lane_segment.get_direction_vector()
        
        context = {
            'lane_type': lane_segment.lane_type,
            'adjacency_ratio': adjacency_analysis.adjacency_ratio,
            'adjacency_length': adjacency_analysis.adjacency_length,
            'visible_length': visible_length,
            'gap_distance': adjacency_analysis.gap_distance,
            'direction': direction,
            'occlusion_type': adjacency_analysis.occlusion_type,
            'contact_points': adjacency_analysis.contact_points,
            'lane_bbox': lane_segment.bbox,
            'occlusion_region': lane_mask.occlusion_regions[adjacency_analysis.occlusion_id]
        }
        
        return context
    
    def filter_significant_adjacencies(self, 
                                     adjacency_analyses: List[AdjacencyAnalysis]) -> List[AdjacencyAnalysis]:
        """
        过滤显著的邻接关系
        
        Args:
            adjacency_analyses: 邻接分析结果列表
            
        Returns:
            显著邻接关系列表
        """
        significant_adjacencies = []
        
        for analysis in adjacency_analyses:
            # 检查是否满足显著性条件
            if (analysis.confidence >= self.confidence_threshold and
                analysis.adjacency_length >= self.min_adjacency_length and
                analysis.gap_distance <= self.max_gap_distance):
                
                significant_adjacencies.append(analysis)
        
        # 按置信度排序
        significant_adjacencies.sort(key=lambda x: x.confidence, reverse=True)
        
        return significant_adjacencies
