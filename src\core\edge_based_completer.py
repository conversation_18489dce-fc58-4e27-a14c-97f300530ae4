"""
基于边缘推理的标线补全器
实现用户专业理解的双向边缘推理和几何补全
"""

import numpy as np
import cv2
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import logging

from ..models.mask_model import LaneType
from .lane_entity_analyzer import LaneEntity, OcclusionBreak


@dataclass
class EdgeInferenceResult:
    """边缘推理结果"""
    left_edge_path: List[Tuple[int, int]]   # 左边缘推理路径
    right_edge_path: List[Tuple[int, int]]  # 右边缘推理路径
    center_path: List[Tuple[int, int]]      # 中心线推理路径
    width_profile: List[float]              # 宽度分布
    confidence: float                       # 推理置信度


@dataclass
class CompletionResult:
    """补全结果"""
    break_id: int
    lane_type: LaneType
    completed_mask: np.ndarray
    inference_result: EdgeInferenceResult
    completion_method: str
    confidence: float
    completed_pixels: int


class EdgeBasedCompleter:
    """基于边缘推理的补全器"""
    
    def __init__(self, config: Dict = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.interpolation_method = self.config.get('interpolation_method', 'cubic')
        self.smoothing_factor = self.config.get('smoothing_factor', 0.1)
        self.width_transition_smoothness = self.config.get('width_transition_smoothness', 0.2)
        self.perspective_correction = self.config.get('perspective_correction', True)
        
        # 质量控制参数
        self.min_inference_confidence = self.config.get('min_inference_confidence', 0.3)
        self.max_curvature_change = self.config.get('max_curvature_change', 0.5)
        self.max_width_change_ratio = self.config.get('max_width_change_ratio', 0.5)
    
    def complete_occlusion_breaks(self, 
                                entities: List[LaneEntity],
                                breaks: List[OcclusionBreak],
                                image_shape: Tuple[int, int]) -> List[CompletionResult]:
        """
        补全遮挡断裂
        
        Args:
            entities: 标线实体列表
            breaks: 遮挡断裂列表
            image_shape: 图像尺寸 (height, width)
            
        Returns:
            补全结果列表
        """
        print(f"开始补全 {len(breaks)} 个遮挡断裂...")
        
        completion_results = []
        
        for i, break_obj in enumerate(breaks):
            entity = entities[break_obj.entity_id]
            
            print(f"\n补全断裂 {i+1}/{len(breaks)}: {entity.lane_type.value}")
            print(f"  断裂位置: {break_obj.break_start} -> {break_obj.break_end}")
            print(f"  起点宽度: {break_obj.start_width:.1f}, 终点宽度: {break_obj.end_width:.1f}")
            
            try:
                # 根据标线类型选择补全方法
                if entity.lane_type == LaneType.SOLID_LINE:
                    result = self._complete_solid_line(break_obj, entity, image_shape)
                elif entity.lane_type == LaneType.DASHED_LINE:
                    result = self._complete_dashed_line(break_obj, entity, image_shape)
                elif entity.lane_type == LaneType.DOUBLE_LINE:
                    result = self._complete_double_line(break_obj, entity, image_shape)
                elif entity.lane_type == LaneType.STOP_LINE:
                    result = self._complete_stop_line(break_obj, entity, image_shape)
                elif entity.lane_type == LaneType.ZEBRA:
                    result = self._complete_zebra_line(break_obj, entity, image_shape)
                elif entity.lane_type == LaneType.GRID_AREA:
                    result = self._complete_grid_area(break_obj, entity, image_shape)
                elif entity.lane_type == LaneType.DIVERGE_AREA:
                    result = self._complete_diverge_area(break_obj, entity, image_shape)
                else:
                    # 默认使用实线方法
                    result = self._complete_solid_line(break_obj, entity, image_shape)
                
                if result and result.completed_pixels > 0:
                    completion_results.append(result)
                    print(f"  ✅ 补全成功: {result.completed_pixels} 像素, "
                          f"置信度: {result.confidence:.3f}")
                else:
                    print(f"  ❌ 补全失败: 没有生成有效像素")
                    
            except Exception as e:
                print(f"  ❌ 补全执行出错: {e}")
                self.logger.error(f"Completion execution failed: {e}")
        
        return completion_results
    
    def _complete_solid_line(self, break_obj: OcclusionBreak, entity: LaneEntity, 
                           image_shape: Tuple[int, int]) -> Optional[CompletionResult]:
        """
        实线补全：连续、可弯曲的长矩形
        基于双边缘推理
        """
        print("    执行实线补全...")
        
        # 1. 双向边缘推理
        inference_result = self._perform_edge_inference(break_obj, entity)
        
        if inference_result.confidence < self.min_inference_confidence:
            print(f"    推理置信度过低: {inference_result.confidence:.3f}")
            return None
        
        print(f"    边缘推理成功: 置信度 {inference_result.confidence:.3f}")
        print(f"    推理路径长度: {len(inference_result.center_path)}")
        
        # 2. 生成补全mask
        completed_mask = self._generate_solid_line_mask(
            inference_result, image_shape
        )
        
        # 3. 应用平滑处理
        completed_mask = self._apply_smoothing(completed_mask)
        
        # 4. 质量验证
        if not self._validate_completion_quality(completed_mask, inference_result):
            print("    补全质量验证失败")
            return None
        
        completed_pixels = np.sum(completed_mask)
        
        return CompletionResult(
            break_id=id(break_obj),
            lane_type=entity.lane_type,
            completed_mask=completed_mask,
            inference_result=inference_result,
            completion_method="solid_line_edge_inference",
            confidence=inference_result.confidence,
            completed_pixels=completed_pixels
        )
    
    def _complete_dashed_line(self, break_obj: OcclusionBreak, entity: LaneEntity,
                            image_shape: Tuple[int, int]) -> Optional[CompletionResult]:
        """
        虚线补全：连续、间断、可弯曲的矩形
        基于段长/间隔模式和边缘推理
        """
        print("    执行虚线补全...")
        
        # 1. 分析虚线模式
        if not entity.dash_segments:
            print("    没有虚线段信息，使用实线方法")
            return self._complete_solid_line(break_obj, entity, image_shape)
        
        # 2. 计算虚线参数
        dash_params = self._calculate_dash_parameters(entity.dash_segments)
        print(f"    虚线参数: 段长={dash_params['avg_length']:.1f}, "
              f"间隔={dash_params['avg_gap']:.1f}")
        
        # 3. 边缘推理
        inference_result = self._perform_edge_inference(break_obj, entity)
        
        if inference_result.confidence < self.min_inference_confidence:
            return None
        
        # 4. 生成虚线模式
        completed_mask = self._generate_dashed_line_mask(
            inference_result, dash_params, image_shape
        )
        
        completed_mask = self._apply_smoothing(completed_mask)
        completed_pixels = np.sum(completed_mask)
        
        return CompletionResult(
            break_id=id(break_obj),
            lane_type=entity.lane_type,
            completed_mask=completed_mask,
            inference_result=inference_result,
            completion_method="dashed_line_pattern_inference",
            confidence=inference_result.confidence,
            completed_pixels=completed_pixels
        )
    
    def _complete_double_line(self, break_obj: OcclusionBreak, entity: LaneEntity,
                            image_shape: Tuple[int, int]) -> Optional[CompletionResult]:
        """双实线补全：保持平行关系"""
        print("    执行双实线补全...")
        
        # 双实线可以使用实线的方法，但需要考虑平行关系
        # 这里简化为实线处理
        result = self._complete_solid_line(break_obj, entity, image_shape)
        if result:
            result.completion_method = "double_line_parallel_inference"
        return result
    
    def _complete_stop_line(self, break_obj: OcclusionBreak, entity: LaneEntity,
                          image_shape: Tuple[int, int]) -> Optional[CompletionResult]:
        """停止线补全：垂直于其他线条"""
        print("    执行停止线补全...")
        
        # 停止线通常是直的，可以使用简化的推理
        result = self._complete_solid_line(break_obj, entity, image_shape)
        if result:
            result.completion_method = "stop_line_perpendicular_inference"
        return result
    
    def _complete_zebra_line(self, break_obj: OcclusionBreak, entity: LaneEntity,
                           image_shape: Tuple[int, int]) -> Optional[CompletionResult]:
        """斑马线补全：多条平行等长线"""
        print("    执行斑马线补全...")
        
        # 斑马线补全比较复杂，这里简化处理
        result = self._complete_solid_line(break_obj, entity, image_shape)
        if result:
            result.completion_method = "zebra_parallel_lines_inference"
        return result
    
    def _complete_grid_area(self, break_obj: OcclusionBreak, entity: LaneEntity,
                          image_shape: Tuple[int, int]) -> Optional[CompletionResult]:
        """网格区补全：X型填充"""
        print("    执行网格区补全...")
        
        # 网格区需要特殊的填充逻辑
        completed_mask = self._generate_grid_pattern_mask(break_obj, entity, image_shape)
        completed_pixels = np.sum(completed_mask)
        
        # 创建简化的推理结果
        inference_result = EdgeInferenceResult(
            left_edge_path=[],
            right_edge_path=[],
            center_path=[],
            width_profile=[],
            confidence=0.8
        )
        
        return CompletionResult(
            break_id=id(break_obj),
            lane_type=entity.lane_type,
            completed_mask=completed_mask,
            inference_result=inference_result,
            completion_method="grid_area_pattern_fill",
            confidence=0.8,
            completed_pixels=completed_pixels
        )
    
    def _complete_diverge_area(self, break_obj: OcclusionBreak, entity: LaneEntity,
                             image_shape: Tuple[int, int]) -> Optional[CompletionResult]:
        """导流区补全：V形或菱形填充"""
        print("    执行导流区补全...")
        
        # 导流区使用类似网格的方法
        return self._complete_grid_area(break_obj, entity, image_shape)
    
    def _perform_edge_inference(self, break_obj: OcclusionBreak, entity: LaneEntity) -> EdgeInferenceResult:
        """
        执行边缘推理
        从断裂两端向中间推理边缘路径
        """
        # 1. 获取断裂两端的边缘信息
        start_left = np.array(break_obj.start_edge_left)
        start_right = np.array(break_obj.start_edge_right)
        start_direction = break_obj.start_direction
        start_width = break_obj.start_width
        
        end_left = np.array(break_obj.end_edge_left)
        end_right = np.array(break_obj.end_edge_right)
        end_direction = break_obj.end_direction
        end_width = break_obj.end_width
        
        print(f"      起点边缘: {start_left} - {start_right}, 方向: {start_direction}")
        print(f"      终点边缘: {end_left} - {end_right}, 方向: {end_direction}")
        
        # 2. 推理中心线路径
        center_path = self._interpolate_center_path(
            break_obj.break_start, break_obj.break_end,
            start_direction, end_direction
        )
        
        # 3. 推理宽度分布
        width_profile = self._interpolate_width_profile(
            start_width, end_width, len(center_path)
        )
        
        # 4. 推理左右边缘路径
        left_edge_path, right_edge_path = self._interpolate_edge_paths(
            center_path, width_profile, start_direction, end_direction
        )
        
        # 5. 计算推理置信度
        confidence = self._calculate_inference_confidence(
            start_direction, end_direction, start_width, end_width
        )
        
        return EdgeInferenceResult(
            left_edge_path=left_edge_path,
            right_edge_path=right_edge_path,
            center_path=center_path,
            width_profile=width_profile,
            confidence=confidence
        )
    
    def _interpolate_center_path(self, start_point: Tuple[int, int], end_point: Tuple[int, int],
                               start_direction: np.ndarray, end_direction: np.ndarray) -> List[Tuple[int, int]]:
        """插值中心线路径"""
        start = np.array(start_point)
        end = np.array(end_point)
        
        # 计算路径长度
        distance = np.linalg.norm(end - start)
        num_points = max(10, int(distance / 2))  # 每2像素一个点
        
        # 使用贝塞尔曲线插值
        t_values = np.linspace(0, 1, num_points)
        
        # 控制点：基于方向向量
        control1 = start + start_direction * distance * 0.3
        control2 = end - end_direction * distance * 0.3
        
        path_points = []
        for t in t_values:
            # 三次贝塞尔曲线
            point = (1-t)**3 * start + 3*(1-t)**2*t * control1 + 3*(1-t)*t**2 * control2 + t**3 * end
            path_points.append((int(point[0]), int(point[1])))
        
        return path_points
    
    def _interpolate_width_profile(self, start_width: float, end_width: float, num_points: int) -> List[float]:
        """插值宽度分布"""
        if num_points <= 1:
            return [start_width]
        
        # 线性插值宽度
        widths = []
        for i in range(num_points):
            t = i / (num_points - 1)
            width = start_width * (1 - t) + end_width * t
            widths.append(width)
        
        return widths
    
    def _interpolate_edge_paths(self, center_path: List[Tuple[int, int]], 
                              width_profile: List[float],
                              start_direction: np.ndarray, 
                              end_direction: np.ndarray) -> Tuple[List[Tuple[int, int]], List[Tuple[int, int]]]:
        """插值边缘路径"""
        left_path = []
        right_path = []
        
        for i, (center_point, width) in enumerate(zip(center_path, width_profile)):
            # 计算当前点的方向（插值）
            if len(center_path) > 1:
                t = i / (len(center_path) - 1)
                direction = start_direction * (1 - t) + end_direction * t
                direction = direction / np.linalg.norm(direction)
            else:
                direction = start_direction
            
            # 计算垂直方向
            perpendicular = np.array([-direction[1], direction[0]])
            
            # 计算左右边缘点
            center = np.array(center_point)
            left_point = center + perpendicular * width / 2
            right_point = center - perpendicular * width / 2
            
            left_path.append((int(left_point[0]), int(left_point[1])))
            right_path.append((int(right_point[0]), int(right_point[1])))
        
        return left_path, right_path
    
    def _calculate_inference_confidence(self, start_direction: np.ndarray, end_direction: np.ndarray,
                                      start_width: float, end_width: float) -> float:
        """计算推理置信度"""
        # 基于方向一致性和宽度一致性计算置信度
        
        # 方向一致性
        direction_similarity = np.dot(start_direction, end_direction)
        direction_confidence = (direction_similarity + 1) / 2  # 归一化到[0,1]
        
        # 宽度一致性
        width_ratio = min(start_width, end_width) / max(start_width, end_width)
        width_confidence = width_ratio
        
        # 综合置信度
        overall_confidence = (direction_confidence * 0.6 + width_confidence * 0.4)
        
        return overall_confidence
    
    def _generate_solid_line_mask(self, inference_result: EdgeInferenceResult, 
                                image_shape: Tuple[int, int]) -> np.ndarray:
        """生成实线mask"""
        mask = np.zeros(image_shape, dtype=np.uint8)
        
        # 使用左右边缘路径填充
        if len(inference_result.left_edge_path) > 0 and len(inference_result.right_edge_path) > 0:
            # 创建多边形点
            left_points = np.array(inference_result.left_edge_path)
            right_points = np.array(inference_result.right_edge_path[::-1])  # 反向
            
            polygon_points = np.vstack([left_points, right_points])
            
            # 填充多边形
            cv2.fillPoly(mask, [polygon_points], 1)
        
        return mask
    
    def _calculate_dash_parameters(self, dash_segments: List[Dict]) -> Dict:
        """计算虚线参数"""
        if not dash_segments:
            return {'avg_length': 20, 'avg_gap': 15, 'avg_width': 3}
        
        lengths = [seg['length'] for seg in dash_segments]
        widths = [seg['width'] for seg in dash_segments]
        
        # 计算间隔（简化）
        centers = [seg['center'] for seg in dash_segments]
        gaps = []
        for i in range(len(centers) - 1):
            gap = np.sqrt((centers[i+1][0] - centers[i][0])**2 + 
                         (centers[i+1][1] - centers[i][1])**2)
            gaps.append(gap)
        
        return {
            'avg_length': np.mean(lengths),
            'avg_gap': np.mean(gaps) if gaps else np.mean(lengths),
            'avg_width': np.mean(widths)
        }
    
    def _generate_dashed_line_mask(self, inference_result: EdgeInferenceResult,
                                 dash_params: Dict, image_shape: Tuple[int, int]) -> np.ndarray:
        """生成虚线mask"""
        mask = np.zeros(image_shape, dtype=np.uint8)
        
        center_path = inference_result.center_path
        width_profile = inference_result.width_profile
        
        if len(center_path) == 0:
            return mask
        
        # 沿中心线生成虚线段
        dash_length = dash_params['avg_length']
        gap_length = dash_params['avg_gap']
        
        current_distance = 0
        is_dash = True
        
        for i in range(len(center_path) - 1):
            p1 = np.array(center_path[i])
            p2 = np.array(center_path[i + 1])
            segment_length = np.linalg.norm(p2 - p1)
            
            if is_dash:
                # 绘制虚线段
                width = width_profile[i] if i < len(width_profile) else 3
                cv2.line(mask, tuple(p1.astype(int)), tuple(p2.astype(int)), 1, int(width))
            
            current_distance += segment_length
            
            # 切换虚线/间隔状态
            if is_dash and current_distance >= dash_length:
                is_dash = False
                current_distance = 0
            elif not is_dash and current_distance >= gap_length:
                is_dash = True
                current_distance = 0
        
        return mask
    
    def _generate_grid_pattern_mask(self, break_obj: OcclusionBreak, entity: LaneEntity,
                                  image_shape: Tuple[int, int]) -> np.ndarray:
        """生成网格模式mask"""
        mask = np.zeros(image_shape, dtype=np.uint8)
        
        # 获取遮挡区域的边界
        occlusion_coords = np.where(break_obj.occlusion_region)
        if len(occlusion_coords[0]) == 0:
            return mask
        
        y_min, y_max = np.min(occlusion_coords[0]), np.max(occlusion_coords[0])
        x_min, x_max = np.min(occlusion_coords[1]), np.max(occlusion_coords[1])
        
        # 生成网格模式
        grid_spacing = 15
        line_width = 2
        
        # 水平线
        for y in range(y_min, y_max, grid_spacing):
            cv2.line(mask, (x_min, y), (x_max, y), 1, line_width)
        
        # 垂直线
        for x in range(x_min, x_max, grid_spacing):
            cv2.line(mask, (x, y_min), (x, y_max), 1, line_width)
        
        return mask
    
    def _apply_smoothing(self, mask: np.ndarray) -> np.ndarray:
        """应用平滑处理"""
        if self.smoothing_factor <= 0:
            return mask
        
        kernel_size = max(3, int(5 * self.smoothing_factor))
        kernel = np.ones((kernel_size, kernel_size), np.uint8)
        
        # 形态学闭运算
        smoothed = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        
        return smoothed
    
    def _validate_completion_quality(self, mask: np.ndarray, inference_result: EdgeInferenceResult) -> bool:
        """验证补全质量"""
        # 简化的质量验证
        if np.sum(mask) == 0:
            return False
        
        if inference_result.confidence < self.min_inference_confidence:
            return False
        
        return True
