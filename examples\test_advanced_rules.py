"""
测试高级规则系统
基于用户专业理解的规则推理测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import cv2
from src.models.mask_model import LaneMask, LaneType, OcclusionType
from src.models.advanced_rule_set import create_advanced_rule_set
from src.core.occlusion_detector import OcclusionDetector
from src.utils.mask_processor import MaskProcessor


def test_advanced_solid_line_completion():
    """测试高级实线补全"""
    print("=== 测试高级实线补全 ===")
    
    # 1. 加载真实数据
    image = cv2.imread("data/images/DJI_20220915153843_0320.JPG")
    mask = cv2.imread("data/masks/DJI_20220915153843_0320.png", cv2.IMREAD_GRAYSCALE)
    
    if image is None or mask is None:
        print("无法加载数据文件")
        return
    
    # 缩放数据
    scale_factor = 0.25
    image = cv2.resize(image, None, fx=scale_factor, fy=scale_factor)
    mask = cv2.resize(mask, None, fx=scale_factor, fy=scale_factor, interpolation=cv2.INTER_NEAREST)
    
    # 2. 解析标线
    processor = MaskProcessor()
    lane_masks, lane_types, _, _ = processor.parse_grayscale_mask(mask)
    
    # 3. 创建标线mask对象
    lane_mask = LaneMask(image.shape[:2])
    solid_line_id = None
    
    for name, lane_mask_data in lane_masks.items():
        lane_type = lane_types[name]
        lane_id = lane_mask.add_lane_segment(lane_type, lane_mask_data)
        if lane_type == LaneType.SOLID_LINE:
            solid_line_id = lane_id
            print(f"找到单实线，ID: {lane_id}")
    
    if solid_line_id is None:
        print("未找到单实线")
        return
    
    # 4. 创建遮挡区域
    solid_mask = lane_masks['solid_line_3']
    solid_pixels = np.where(solid_mask > 0)
    
    if len(solid_pixels[0]) == 0:
        print("单实线没有像素")
        return
    
    # 在实线中间创建遮挡
    mid_idx = len(solid_pixels[0]) // 2
    center_y = solid_pixels[0][mid_idx]
    center_x = solid_pixels[1][mid_idx]
    
    occlusion_mask = np.zeros_like(mask)
    width, height = 100, 60
    y1 = max(0, center_y - height // 2)
    y2 = min(mask.shape[0], center_y + height // 2)
    x1 = max(0, center_x - width // 2)
    x2 = min(mask.shape[1], center_x + width // 2)
    occlusion_mask[y1:y2, x1:x2] = 1
    
    occlusion_id = lane_mask.add_occlusion_region(OcclusionType.DYNAMIC, occlusion_mask)
    print(f"创建遮挡区域，ID: {occlusion_id}")
    
    # 5. 检测遮挡
    detector = OcclusionDetector({'confidence_threshold': 0.05, 'overlap_threshold': 0.01})
    occlusion_analyses = detector.detect_occlusions(
        lane_mask, {'car': occlusion_mask}, {'car': OcclusionType.DYNAMIC}
    )
    
    print(f"检测到 {len(occlusion_analyses)} 个遮挡")
    for analysis in occlusion_analyses:
        print(f"  - 标线 {analysis.lane_id} 被遮挡 {analysis.overlap_ratio:.2%}")
    
    # 6. 创建上下文
    if len(occlusion_analyses) > 0:
        analysis = occlusion_analyses[0]
        lane_segment = lane_mask.lane_segments[analysis.lane_id]
        context = detector.create_occlusion_context(lane_segment, analysis, lane_mask)
        
        print(f"上下文信息:")
        print(f"  - 标线类型: {context['lane_type']}")
        print(f"  - 遮挡比例: {context['occlusion_ratio']:.3f}")
        print(f"  - 可见长度: {context['visible_length']}")
        
        # 7. 测试高级规则匹配
        rule_set = create_advanced_rule_set()
        matching_rules = rule_set.find_matching_rules(context)
        
        print(f"匹配的规则数: {len(matching_rules)}")
        for rule, confidence in matching_rules:
            print(f"  - {rule.rule_id}: {rule.name} (置信度: {confidence:.3f})")
            
            # 8. 执行补全
            if len(rule.actions) > 0:
                action = rule.actions[0]
                lane_segment = lane_mask.lane_segments[analysis.lane_id]
                occlusion_region = lane_mask.occlusion_regions[analysis.occlusion_id]
                
                try:
                    completed_mask = action.execute(lane_segment, occlusion_region, context)
                    print(f"    补全结果: {np.sum(completed_mask)} 像素")
                    
                    # 保存结果
                    output_dir = "examples/output_advanced_rules"
                    os.makedirs(output_dir, exist_ok=True)
                    
                    cv2.imwrite(f"{output_dir}/solid_line_completion.png", completed_mask * 255)
                    cv2.imwrite(f"{output_dir}/solid_line_occlusion.png", occlusion_mask * 255)
                    cv2.imwrite(f"{output_dir}/solid_line_original.png", solid_mask * 255)
                    
                    # 创建对比图
                    comparison = create_completion_comparison(
                        image, solid_mask, occlusion_mask, completed_mask
                    )
                    cv2.imwrite(f"{output_dir}/solid_line_comparison.jpg", comparison)
                    
                except Exception as e:
                    print(f"    补全执行失败: {e}")


def test_advanced_dashed_line_completion():
    """测试高级虚线补全"""
    print("\n=== 测试高级虚线补全 ===")
    
    # 1. 加载真实数据
    image = cv2.imread("data/images/DJI_20220915153843_0320.JPG")
    mask = cv2.imread("data/masks/DJI_20220915153843_0320.png", cv2.IMREAD_GRAYSCALE)
    
    if image is None or mask is None:
        print("无法加载数据文件")
        return
    
    # 缩放数据
    scale_factor = 0.25
    image = cv2.resize(image, None, fx=scale_factor, fy=scale_factor)
    mask = cv2.resize(mask, None, fx=scale_factor, fy=scale_factor, interpolation=cv2.INTER_NEAREST)
    
    # 2. 解析标线
    processor = MaskProcessor()
    lane_masks, lane_types, _, _ = processor.parse_grayscale_mask(mask)
    
    # 3. 创建标线mask对象
    lane_mask = LaneMask(image.shape[:2])
    dashed_line_id = None
    
    for name, lane_mask_data in lane_masks.items():
        lane_type = lane_types[name]
        lane_id = lane_mask.add_lane_segment(lane_type, lane_mask_data)
        if lane_type == LaneType.DASHED_LINE:
            dashed_line_id = lane_id
            print(f"找到虚线，ID: {lane_id}")
    
    if dashed_line_id is None:
        print("未找到虚线")
        return
    
    # 4. 创建遮挡区域
    dashed_mask = lane_masks['dashed_line_4']
    dashed_pixels = np.where(dashed_mask > 0)
    
    if len(dashed_pixels[0]) == 0:
        print("虚线没有像素")
        return
    
    # 在虚线中间创建遮挡
    quarter_idx = len(dashed_pixels[0]) // 4
    center_y = dashed_pixels[0][quarter_idx]
    center_x = dashed_pixels[1][quarter_idx]
    
    occlusion_mask = np.zeros_like(mask)
    width, height = 80, 40
    y1 = max(0, center_y - height // 2)
    y2 = min(mask.shape[0], center_y + height // 2)
    x1 = max(0, center_x - width // 2)
    x2 = min(mask.shape[1], center_x + width // 2)
    occlusion_mask[y1:y2, x1:x2] = 1
    
    occlusion_id = lane_mask.add_occlusion_region(OcclusionType.DYNAMIC, occlusion_mask)
    print(f"创建遮挡区域，ID: {occlusion_id}")
    
    # 5. 检测遮挡
    detector = OcclusionDetector({'confidence_threshold': 0.05, 'overlap_threshold': 0.01})
    occlusion_analyses = detector.detect_occlusions(
        lane_mask, {'car': occlusion_mask}, {'car': OcclusionType.DYNAMIC}
    )
    
    print(f"检测到 {len(occlusion_analyses)} 个遮挡")
    for analysis in occlusion_analyses:
        print(f"  - 标线 {analysis.lane_id} 被遮挡 {analysis.overlap_ratio:.2%}")
    
    # 6. 创建上下文
    if len(occlusion_analyses) > 0:
        analysis = occlusion_analyses[0]
        lane_segment = lane_mask.lane_segments[analysis.lane_id]
        context = detector.create_occlusion_context(lane_segment, analysis, lane_mask)
        
        print(f"上下文信息:")
        print(f"  - 标线类型: {context['lane_type']}")
        print(f"  - 遮挡比例: {context['occlusion_ratio']:.3f}")
        print(f"  - 可见长度: {context['visible_length']}")
        
        # 7. 测试高级规则匹配
        rule_set = create_advanced_rule_set()
        matching_rules = rule_set.find_matching_rules(context)
        
        print(f"匹配的规则数: {len(matching_rules)}")
        for rule, confidence in matching_rules:
            print(f"  - {rule.rule_id}: {rule.name} (置信度: {confidence:.3f})")
            
            # 8. 执行补全
            if len(rule.actions) > 0:
                action = rule.actions[0]
                lane_segment = lane_mask.lane_segments[analysis.lane_id]
                occlusion_region = lane_mask.occlusion_regions[analysis.occlusion_id]
                
                try:
                    completed_mask = action.execute(lane_segment, occlusion_region, context)
                    print(f"    补全结果: {np.sum(completed_mask)} 像素")
                    
                    # 保存结果
                    output_dir = "examples/output_advanced_rules"
                    os.makedirs(output_dir, exist_ok=True)
                    
                    cv2.imwrite(f"{output_dir}/dashed_line_completion.png", completed_mask * 255)
                    cv2.imwrite(f"{output_dir}/dashed_line_occlusion.png", occlusion_mask * 255)
                    cv2.imwrite(f"{output_dir}/dashed_line_original.png", dashed_mask * 255)
                    
                    # 创建对比图
                    comparison = create_completion_comparison(
                        image, dashed_mask, occlusion_mask, completed_mask
                    )
                    cv2.imwrite(f"{output_dir}/dashed_line_comparison.jpg", comparison)
                    
                except Exception as e:
                    print(f"    补全执行失败: {e}")


def create_completion_comparison(image, original_mask, occlusion_mask, completed_mask):
    """创建补全对比图"""
    h, w = image.shape[:2]
    result = np.zeros((h, w * 4, 3), dtype=np.uint8)
    
    # 原图
    result[:, :w] = image
    
    # 原始标线（白色）
    original_vis = np.zeros((h, w, 3), dtype=np.uint8)
    original_vis[original_mask > 0] = [255, 255, 255]
    result[:, w:w*2] = original_vis
    
    # 遮挡区域（红色）
    occlusion_vis = original_vis.copy()
    occlusion_vis[occlusion_mask > 0] = [0, 0, 255]
    result[:, w*2:w*3] = occlusion_vis
    
    # 补全结果（绿色补全，白色原始）
    completion_vis = original_vis.copy()
    completion_vis[completed_mask > 0] = [0, 255, 0]
    result[:, w*3:] = completion_vis
    
    # 添加标题
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(result, "Original", (10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Lane Mask", (w + 10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "With Occlusion", (w*2 + 10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Completed", (w*3 + 10, 25), font, 0.7, (255, 255, 255), 2)
    
    return result


def main():
    """主函数"""
    print("开始测试高级规则系统...")
    
    try:
        test_advanced_solid_line_completion()
        test_advanced_dashed_line_completion()
        
        print("\n=== 测试完成 ===")
        print("结果已保存到 examples/output_advanced_rules/")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
