"""
测试修正的补全算法
验证补全算法是否真正生成补全像素
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import cv2
from src.models.mask_model import LaneMask, LaneType, OcclusionType
from src.core.adjacency_detector import AdjacencyDetector
from src.models.advanced_rule_set import create_advanced_rule_set
from src.utils.mask_processor import MaskProcessor


def test_fixed_completion_real_data():
    """测试修正的补全算法在真实数据上的效果"""
    print("=== 测试修正的补全算法 ===")
    
    # 1. 加载真实数据
    print("1. 加载真实数据...")
    image_path = "data/images/DJI_20220915153843_0320.JPG"
    mask_path = "data/masks/DJI_20220915153843_0320.png"
    
    try:
        image = cv2.imread(image_path)
        mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
        
        if image is None or mask is None:
            print("无法加载数据文件")
            return
        
        # 缩放数据
        scale_factor = 0.25
        image = cv2.resize(image, None, fx=scale_factor, fy=scale_factor)
        mask = cv2.resize(mask, None, fx=scale_factor, fy=scale_factor, interpolation=cv2.INTER_NEAREST)
        
        print(f"   缩放后图像尺寸: {image.shape}")
        
    except Exception as e:
        print(f"   加载数据时出错: {e}")
        return
    
    # 2. 解析标线
    print("2. 解析标线数据...")
    processor = MaskProcessor()
    lane_masks, lane_types, _, _ = processor.parse_grayscale_mask(mask)
    
    print(f"   检测到 {len(lane_masks)} 种标线类型")
    
    # 3. 创建标线mask对象
    print("3. 创建标线mask对象...")
    lane_mask = LaneMask(image.shape[:2])
    for name, lane_mask_data in lane_masks.items():
        lane_type = lane_types[name]
        lane_id = lane_mask.add_lane_segment(lane_type, lane_mask_data)
    
    # 4. 检测遮挡物和邻接关系
    print("4. 检测遮挡物和邻接关系...")
    detector = AdjacencyDetector({
        'max_gap_distance': 20,
        'min_adjacency_length': 15,
        'confidence_threshold': 0.3
    })
    
    # 检测遮挡物
    occlusion_masks, occlusion_types = detector.detect_occlusions_from_mask(mask)
    print(f"   检测到 {len(occlusion_masks)} 种遮挡物")
    
    if len(occlusion_masks) == 0:
        print("   没有检测到遮挡物")
        return
    
    # 检测邻接关系
    adjacency_analyses = detector.detect_adjacencies(lane_mask, occlusion_masks, occlusion_types)
    significant_adjacencies = detector.filter_significant_adjacencies(adjacency_analyses)
    
    print(f"   检测到 {len(significant_adjacencies)} 个显著邻接关系")
    
    if len(significant_adjacencies) == 0:
        print("   没有显著邻接关系")
        return
    
    # 5. 测试修正的补全算法
    print("5. 测试修正的补全算法...")
    rule_set = create_advanced_rule_set()
    
    total_completed_pixels = 0
    successful_completions = 0
    
    output_dir = "examples/output_fixed_completion"
    os.makedirs(output_dir, exist_ok=True)
    
    for i, analysis in enumerate(significant_adjacencies[:5]):  # 只测试前5个
        lane_segment = lane_mask.lane_segments[analysis.lane_id]
        occlusion_region = lane_mask.occlusion_regions[analysis.occlusion_id]
        
        print(f"\n   测试邻接关系 {i+1}:")
        print(f"   - 标线类型: {lane_segment.lane_type.value}")
        print(f"   - 邻接比例: {analysis.adjacency_ratio:.2%}")
        print(f"   - 置信度: {analysis.confidence:.3f}")
        
        # 创建上下文
        context = detector.create_adjacency_context(lane_segment, analysis, lane_mask)
        
        # 查找匹配规则
        matching_rules = rule_set.find_matching_rules(context)
        
        if len(matching_rules) > 0:
            rule, rule_confidence = matching_rules[0]  # 取最佳规则
            print(f"   - 匹配规则: {rule.rule_id} (置信度: {rule_confidence:.3f})")
            
            # 执行补全
            if len(rule.actions) > 0:
                action = rule.actions[0]
                
                try:
                    print(f"   - 执行补全动作: {action.action_type}")
                    completed_mask = action.execute(lane_segment, occlusion_region, context)
                    completed_pixels = np.sum(completed_mask)
                    
                    print(f"   - 补全结果: {completed_pixels} 像素")
                    
                    if completed_pixels > 0:
                        successful_completions += 1
                        total_completed_pixels += completed_pixels
                        
                        # 保存补全结果
                        cv2.imwrite(f"{output_dir}/completion_{i}_{rule.rule_id}.png", 
                                   completed_mask * 255)
                        
                        # 创建对比图
                        comparison = create_completion_comparison(
                            image, lane_segment.mask, occlusion_region.mask, completed_mask
                        )
                        cv2.imwrite(f"{output_dir}/comparison_{i}.jpg", comparison)
                        
                        print(f"   ✅ 补全成功！")
                    else:
                        print(f"   ❌ 补全失败：没有生成像素")
                        
                except Exception as e:
                    print(f"   ❌ 补全执行出错: {e}")
        else:
            print(f"   - 没有匹配的规则")
    
    # 6. 创建综合可视化
    print("6. 创建综合可视化...")
    comprehensive_vis = create_comprehensive_completion_visualization(
        image, mask, lane_mask, occlusion_masks, significant_adjacencies, output_dir
    )
    cv2.imwrite(f"{output_dir}/comprehensive_completion_test.jpg", comprehensive_vis)
    
    # 7. 显示总结
    print(f"\n=== 修正补全算法测试总结 ===")
    print(f"测试的邻接关系: {min(5, len(significant_adjacencies))}")
    print(f"成功补全: {successful_completions}")
    print(f"总补全像素: {total_completed_pixels}")
    print(f"平均每次补全: {total_completed_pixels / max(successful_completions, 1):.1f} 像素")
    
    if successful_completions > 0:
        print(f"✅ 修正的补全算法工作正常！")
    else:
        print(f"❌ 补全算法仍有问题，需要进一步调试")
    
    print(f"结果已保存到: {output_dir}")


def create_completion_comparison(image, lane_mask, occlusion_mask, completed_mask):
    """创建补全对比图"""
    h, w = image.shape[:2]
    result = np.zeros((h, w * 4, 3), dtype=np.uint8)
    
    # 原图
    result[:, :w] = image
    
    # 标线（白色）
    lane_vis = np.zeros((h, w, 3), dtype=np.uint8)
    lane_vis[lane_mask > 0] = [255, 255, 255]
    result[:, w:w*2] = lane_vis
    
    # 遮挡物（红色）
    occlusion_vis = lane_vis.copy()
    occlusion_vis[occlusion_mask > 0] = [255, 0, 0]
    result[:, w*2:w*3] = occlusion_vis
    
    # 补全结果（绿色补全，白色原始）
    completion_vis = lane_vis.copy()
    completion_vis[completed_mask > 0] = [0, 255, 0]
    result[:, w*3:] = completion_vis
    
    # 添加标题
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(result, "Original", (10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Lane", (w + 10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Occlusion", (w*2 + 10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Completed", (w*3 + 10, 25), font, 0.7, (255, 255, 255), 2)
    
    return result


def create_comprehensive_completion_visualization(image, mask, lane_mask, occlusion_masks, 
                                                adjacencies, output_dir):
    """创建综合补全可视化"""
    h, w = image.shape[:2]
    
    # 创建2x3布局
    result = np.zeros((h * 2, w * 3, 3), dtype=np.uint8)
    
    # 第一行：原图、标线、遮挡物
    result[:h, :w] = image
    
    # 标线可视化
    lane_vis = np.zeros((h, w, 3), dtype=np.uint8)
    for lane_segment in lane_mask.lane_segments.values():
        lane_vis[lane_segment.mask > 0] = [255, 255, 255]
    result[:h, w:w*2] = lane_vis
    
    # 遮挡物可视化
    occlusion_vis = np.zeros((h, w, 3), dtype=np.uint8)
    colors = {'car': [255, 0, 0], 'human': [0, 255, 0], 'clatter': [0, 0, 255], 'obstacle': [255, 255, 0]}
    for name, occlusion_mask in occlusion_masks.items():
        color = colors.get(name, [128, 128, 128])
        occlusion_vis[occlusion_mask > 0] = color
    result[:h, w*2:] = occlusion_vis
    
    # 第二行：邻接关系、需要补全的区域、补全结果
    # 邻接关系
    adjacency_vis = lane_vis.copy()
    for name, occlusion_mask in occlusion_masks.items():
        color = colors.get(name, [128, 128, 128])
        adjacency_vis[occlusion_mask > 0] = color
    
    # 标记邻接点
    for adjacency in adjacencies[:10]:  # 只显示前10个
        for point in adjacency.contact_points[:5]:  # 每个邻接只显示5个点
            x, y = point
            if 0 <= x < w and 0 <= y < h:
                cv2.circle(adjacency_vis, (x, y), 2, (255, 0, 255), -1)
    
    result[h:, :w] = adjacency_vis
    
    # 需要补全的区域（遮挡物区域）
    completion_needed = np.zeros((h, w, 3), dtype=np.uint8)
    for name, occlusion_mask in occlusion_masks.items():
        completion_needed[occlusion_mask > 0] = [255, 255, 0]  # 黄色表示需要补全
    result[h:, w:w*2] = completion_needed
    
    # 补全结果（如果有的话）
    completion_result = lane_vis.copy()
    # 这里可以加载之前保存的补全结果
    result[h:, w*2:] = completion_result
    
    # 添加标题
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(result, "Original", (10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Lanes", (w + 10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Occlusions", (w*2 + 10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Adjacencies", (10, h + 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Need Completion", (w + 10, h + 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Completed", (w*2 + 10, h + 25), font, 0.7, (255, 255, 255), 2)
    
    return result


if __name__ == "__main__":
    try:
        test_fixed_completion_real_data()
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
