"""
灰度图mask测试示例
演示如何处理您的灰度图数据格式
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import cv2
from src.models.mask_model import LaneMask, LaneType, OcclusionType, LANE_TYPE_TO_PIXEL, NON_LANE_PIXELS
from src.core.occlusion_detector import OcclusionDetector
from src.core.rule_engine import RuleEngine
from src.core.structure_completer import StructureCompleter
from src.utils.mask_processor import MaskProcessor
from src.utils.visualization import Visualizer


def create_test_grayscale_mask():
    """创建测试用的灰度图mask"""
    height, width = 480, 640
    
    # 创建背景图像（模拟道路）
    image = np.ones((height, width, 3), dtype=np.uint8) * 80  # 深灰色背景
    
    # 添加道路区域
    cv2.rectangle(image, (0, height//3), (width, 2*height//3), (60, 60, 60), -1)
    
    # 创建灰度mask
    mask = np.zeros((height, width), dtype=np.uint8)
    
    # 1. 单实线 (pixel value = 3)
    cv2.line(mask, (50, height//2), (width-50, height//2), 3, 4)
    
    # 2. 虚线 (pixel value = 4)
    for x in range(50, width-50, 30):  # 虚线模式
        cv2.line(mask, (x, height//2 - 40), (x+15, height//2 - 40), 4, 3)
    
    # 3. 双实线 (pixel value = 5)
    cv2.line(mask, (50, height//2 + 40), (width-50, height//2 + 40), 5, 3)
    cv2.line(mask, (50, height//2 + 48), (width-50, height//2 + 48), 5, 3)
    
    # 4. 停止线 (pixel value = 6)
    cv2.line(mask, (200, height//2 - 80), (400, height//2 - 80), 6, 8)
    
    # 5. 直行箭头 (pixel value = 7)
    # 简化的箭头形状
    arrow_points = np.array([
        [150, height//2 + 100],
        [170, height//2 + 80],
        [160, height//2 + 80],
        [160, height//2 + 60],
        [140, height//2 + 60],
        [140, height//2 + 80],
        [130, height//2 + 80]
    ], np.int32)
    cv2.fillPoly(mask, [arrow_points], 7)
    
    # 6. 导流区 (pixel value = 8)
    # 斜线模式
    for i in range(0, 100, 10):
        cv2.line(mask, (450 + i, height//2 + 60), (450 + i + 40, height//2 + 100), 8, 2)
    
    # 7. 网格区 (pixel value = 9)
    # 网格模式
    for i in range(0, 80, 15):
        cv2.line(mask, (500, height//2 - 60 + i), (580, height//2 - 60 + i), 9, 2)
        cv2.line(mask, (500 + i, height//2 - 60), (500 + i, height//2 + 20), 9, 2)
    
    # 8. 添加遮挡物
    # 车辆遮挡 (pixel value = 1)
    cv2.rectangle(mask, (300, height//2 - 25), (450, height//2 + 25), 1, -1)
    
    # 行人遮挡 (pixel value = 2)
    cv2.ellipse(mask, (130, height//2 + 80), (20, 35), 0, 0, 360, 2, -1)
    
    return image, mask


def run_grayscale_mask_test():
    """运行灰度图mask测试"""
    print("=== 基于规则推理的标线结构补全系统 ===")
    print("=== 灰度图Mask测试 ===")
    
    # 1. 创建测试数据
    print("1. 创建测试灰度图mask...")
    image, grayscale_mask = create_test_grayscale_mask()
    
    print(f"   图像尺寸: {image.shape}")
    print(f"   Mask尺寸: {grayscale_mask.shape}")
    print(f"   Mask像素值范围: {np.min(grayscale_mask)} - {np.max(grayscale_mask)}")
    print(f"   唯一像素值: {np.unique(grayscale_mask)}")
    
    # 2. 初始化处理器
    print("2. 初始化处理器...")
    mask_processor = MaskProcessor()
    detector = OcclusionDetector()
    engine = RuleEngine()
    completer = StructureCompleter()
    visualizer = Visualizer()
    
    # 3. 解析灰度mask
    print("3. 解析灰度mask...")
    lane_masks, lane_types, occlusion_masks, occlusion_types = mask_processor.parse_grayscale_mask(grayscale_mask)
    
    print(f"   检测到标线类型: {len(lane_masks)}")
    for name, lane_type in lane_types.items():
        pixels = np.sum(lane_masks[name])
        print(f"   - {name}: {lane_type.value} ({pixels} 像素)")
    
    print(f"   检测到遮挡物: {len(occlusion_masks)}")
    for name, occlusion_type in occlusion_types.items():
        pixels = np.sum(occlusion_masks[name])
        print(f"   - {name}: {occlusion_type.value} ({pixels} 像素)")
    
    # 4. 创建标线mask对象
    print("4. 创建标线mask对象...")
    lane_mask = LaneMask(image.shape[:2])
    
    # 添加标线段
    for name, mask in lane_masks.items():
        lane_type = lane_types[name]
        lane_id = lane_mask.add_lane_segment(lane_type, mask)
        print(f"   添加标线段: {name} (ID: {lane_id})")
    
    # 5. 检测遮挡
    print("5. 检测遮挡...")
    occlusion_analyses = detector.detect_occlusions(
        lane_mask, occlusion_masks, occlusion_types
    )
    
    print(f"   检测到 {len(occlusion_analyses)} 个遮挡区域")
    for analysis in occlusion_analyses:
        print(f"   - 标线 {analysis.lane_id} 被 {analysis.occlusion_type.value} 遮挡 "
              f"{analysis.overlap_ratio:.2%}, 置信度: {analysis.confidence:.3f}")
    
    # 过滤显著遮挡
    significant_occlusions = detector.filter_significant_occlusions(occlusion_analyses)
    print(f"   其中 {len(significant_occlusions)} 个为显著遮挡")
    
    # 6. 创建上下文信息
    print("6. 创建推理上下文...")
    contexts = []
    for analysis in significant_occlusions:
        lane_segment = lane_mask.lane_segments[analysis.lane_id]
        context = detector.create_occlusion_context(lane_segment, analysis, lane_mask)
        contexts.append(context)
        print(f"   标线 {analysis.lane_id}: 类型={context['lane_type'].value}, "
              f"遮挡比例={context['occlusion_ratio']:.2%}")
    
    # 7. 规则推理
    print("7. 执行规则推理...")
    inference_results = engine.infer_completions(lane_mask, significant_occlusions, contexts)
    
    print(f"   生成 {len(inference_results)} 个推理结果")
    for result in inference_results:
        print(f"   - 规则 {result.rule_id}: 标线 {result.lane_id}, "
              f"置信度: {result.confidence:.3f}")
    
    # 8. 结构补全
    print("8. 执行结构补全...")
    completion_result = completer.complete_structure(lane_mask, inference_results)
    
    print("   补全统计:")
    stats = completion_result.statistics
    print(f"   - 总推理数: {stats['total_inferences']}")
    print(f"   - 成功补全: {stats['successful_completions']}")
    print(f"   - 失败补全: {stats['failed_completions']}")
    print(f"   - 补全像素: {stats['total_completed_pixels']}")
    if stats['successful_completions'] > 0:
        print(f"   - 平均置信度: {stats['average_confidence']:.3f}")
    
    # 9. 生成可视化
    print("9. 生成可视化结果...")
    
    # 创建RGB可视化
    rgb_visualization = mask_processor.create_detailed_rgb_visualization(
        lane_mask, completion_result.completed_mask, completion_result.completion_regions
    )
    
    # 创建对比图像
    comparison_image = mask_processor.create_comparison_image(
        image, completion_result.original_mask, 
        completion_result.completed_mask, completion_result.completion_regions
    )
    
    # 创建补全结果的灰度图
    completed_grayscale = mask_processor.create_mask_from_lane_segments(lane_mask, target_pixel_values=True)
    
    # 10. 保存结果
    print("10. 保存结果...")
    output_dir = "examples/output_grayscale"
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存图像
    cv2.imwrite(f"{output_dir}/original_image.jpg", image)
    cv2.imwrite(f"{output_dir}/original_grayscale_mask.png", grayscale_mask)
    cv2.imwrite(f"{output_dir}/rgb_visualization.jpg", rgb_visualization)
    cv2.imwrite(f"{output_dir}/comparison_view.jpg", comparison_image)
    cv2.imwrite(f"{output_dir}/completed_grayscale_mask.png", completed_grayscale)
    
    # 保存各个标线类型的单独mask
    for name, mask in lane_masks.items():
        cv2.imwrite(f"{output_dir}/lane_{name}.png", mask * 255)
    
    # 保存遮挡物mask
    for name, mask in occlusion_masks.items():
        cv2.imwrite(f"{output_dir}/occlusion_{name}.png", mask * 255)
    
    # 生成详细报告
    report = completer.generate_completion_report(completion_result)
    
    # 添加额外信息
    report['input_data'] = {
        'original_mask_shape': grayscale_mask.shape,
        'unique_pixel_values': np.unique(grayscale_mask).tolist(),
        'detected_lane_types': {name: lane_type.value for name, lane_type in lane_types.items()},
        'detected_occlusions': {name: occlusion_type.value for name, occlusion_type in occlusion_types.items()}
    }
    
    # 保存报告
    import json
    with open(f"{output_dir}/detailed_report.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"   结果已保存到: {output_dir}")
    
    # 11. 显示总结
    print("\n=== 处理总结 ===")
    print(f"输入数据:")
    print(f"  - 图像尺寸: {image.shape}")
    print(f"  - 检测到标线类型: {len(lane_types)}")
    print(f"  - 检测到遮挡物: {len(occlusion_types)}")
    
    print(f"处理结果:")
    print(f"  - 原始标线像素: {report['summary']['original_pixels']}")
    print(f"  - 补全后像素: {report['summary']['completed_pixels']}")
    print(f"  - 新增像素: {report['summary']['completion_pixels']}")
    print(f"  - 补全比例: {report['summary']['completion_ratio']:.2%}")
    
    print("\n灰度图mask测试完成！")
    return completion_result, report


def demonstrate_rule_coverage():
    """演示规则覆盖情况"""
    print("\n=== 规则覆盖演示 ===")
    
    engine = RuleEngine()
    
    print("可用规则:")
    for rule in engine.rule_set.rules:
        print(f"  - {rule.rule_id}: {rule.name}")
        print(f"    优先级: {rule.priority}, 置信度阈值: {rule.confidence_threshold}")
    
    # 测试各种标线类型的规则匹配
    test_lane_types = [
        LaneType.SOLID_LINE,
        LaneType.DASHED_LINE,
        LaneType.DOUBLE_LINE,
        LaneType.STOP_LINE,
        LaneType.ARROW_STRAIGHT,
        LaneType.ARROW_LEFT,
        LaneType.ARROW_RIGHT,
        LaneType.DIVERGE_AREA,
        LaneType.GRID_AREA
    ]
    
    print("\n规则匹配测试:")
    for lane_type in test_lane_types:
        context = {
            'lane_type': lane_type,
            'occlusion_ratio': 0.5,
            'visible_length': 50,
            'direction': (1.0, 0.0)
        }
        
        matching_rules = engine.rule_set.find_matching_rules(context)
        
        print(f"\n{lane_type.value}:")
        if matching_rules:
            for rule, confidence in matching_rules:
                print(f"  ✓ {rule.rule_id}: {rule.name} (置信度: {confidence:.3f})")
        else:
            print(f"  ✗ 无匹配规则")


if __name__ == "__main__":
    try:
        # 运行主测试
        completion_result, report = run_grayscale_mask_test()
        
        # 演示规则覆盖
        demonstrate_rule_coverage()
        
        print("\n所有测试运行完成！")
        
    except Exception as e:
        print(f"测试运行出错: {e}")
        import traceback
        traceback.print_exc()
