"""
可视化工具
用于生成各种可视化结果
"""

import numpy as np
import cv2
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
from typing import Dict, List, Tuple, Optional
import logging


class Visualizer:
    """可视化器"""

    def __init__(self, config: Optional[Dict] = None):
        """
        初始化可视化器

        Args:
            config: 配置参数
        """
        self.config = config or {}

        # 颜色配置
        self.colors = self.config.get('colors', {
            'original': [0, 255, 0],      # 绿色 - 原始标线
            'occluded': [255, 0, 0],      # 红色 - 遮挡区域
            'completed': [0, 0, 255],     # 蓝色 - 补全区域
            'confidence_low': [255, 255, 0],   # 黄色 - 低置信度
            'confidence_high': [0, 255, 255]   # 青色 - 高置信度
        })

        self.line_thickness = self.config.get('line_thickness', 2)
        self.logger = logging.getLogger(__name__)

    def create_comparison_view(self,
                             original_image: np.ndarray,
                             original_mask: np.ndarray,
                             completed_mask: np.ndarray,
                             completion_regions: np.ndarray,
                             confidence_map: np.ndarray) -> np.ndarray:
        """
        创建对比视图

        Args:
            original_image: 原始图像
            original_mask: 原始标线mask
            completed_mask: 补全后的mask
            completion_regions: 补全区域mask
            confidence_map: 置信度图

        Returns:
            对比视图图像
        """
        # 创建四个子图：原图、原始mask、补全结果、置信度图
        h, w = original_image.shape[:2]

        # 创建2x2布局
        result_image = np.zeros((h * 2, w * 2, 3), dtype=np.uint8)

        # 左上：原图 + 原始标线
        overlay1 = self._overlay_mask_on_image(original_image, original_mask,
                                              self.colors['original'])
        result_image[:h, :w] = overlay1

        # 右上：原图 + 补全结果
        overlay2 = self._overlay_mask_on_image(original_image, completed_mask,
                                              self.colors['completed'])
        result_image[:h, w:] = overlay2

        # 左下：补全区域高亮
        overlay3 = self._create_completion_highlight(original_image, original_mask,
                                                   completion_regions)
        result_image[h:, :w] = overlay3

        # 右下：置信度热图
        confidence_vis = self._create_confidence_heatmap(original_image, confidence_map,
                                                        completion_regions)
        result_image[h:, w:] = confidence_vis

        # 添加标题
        result_image = self._add_titles(result_image,
                                       ["Original + Lanes", "Original + Completed",
                                        "Completion Regions", "Confidence Map"])

        return result_image

    def _overlay_mask_on_image(self,
                              image: np.ndarray,
                              mask: np.ndarray,
                              color: List[int],
                              alpha: float = 0.6) -> np.ndarray:
        """
        在图像上叠加mask

        Args:
            image: 原始图像
            mask: 二值mask
            color: 叠加颜色 [B, G, R]
            alpha: 透明度

        Returns:
            叠加后的图像
        """
        overlay = image.copy()

        # 创建彩色mask
        colored_mask = np.zeros_like(image)
        colored_mask[mask > 0] = color

        # 叠加
        result = cv2.addWeighted(overlay, 1 - alpha, colored_mask, alpha, 0)

        return result

    def _create_completion_highlight(self,
                                   image: np.ndarray,
                                   original_mask: np.ndarray,
                                   completion_regions: np.ndarray) -> np.ndarray:
        """
        创建补全区域高亮视图

        Args:
            image: 原始图像
            original_mask: 原始标线mask
            completion_regions: 补全区域mask

        Returns:
            高亮视图
        """
        # 灰度化背景
        gray_bg = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        gray_bg = cv2.cvtColor(gray_bg, cv2.COLOR_GRAY2BGR)

        # 叠加原始标线（绿色）
        result = self._overlay_mask_on_image(gray_bg, original_mask,
                                           self.colors['original'], alpha=0.8)

        # 叠加补全区域（红色高亮）
        result = self._overlay_mask_on_image(result, completion_regions,
                                           self.colors['completed'], alpha=0.9)

        return result

    def _create_confidence_heatmap(self,
                                  image: np.ndarray,
                                  confidence_map: np.ndarray,
                                  completion_regions: np.ndarray) -> np.ndarray:
        """
        创建置信度热图

        Args:
            image: 原始图像
            confidence_map: 置信度图
            completion_regions: 补全区域mask

        Returns:
            置信度热图
        """
        # 只显示补全区域的置信度
        confidence_masked = confidence_map * completion_regions

        # 创建热图
        heatmap = plt.cm.jet(confidence_masked)[:, :, :3]  # 去掉alpha通道
        heatmap = (heatmap * 255).astype(np.uint8)

        # 转换为BGR
        heatmap = cv2.cvtColor(heatmap, cv2.COLOR_RGB2BGR)

        # 与原图混合
        gray_bg = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        gray_bg = cv2.cvtColor(gray_bg, cv2.COLOR_GRAY2BGR)

        # 只在补全区域显示热图
        result = gray_bg.copy()
        result[completion_regions > 0] = heatmap[completion_regions > 0]

        return result

    def _add_titles(self,
                   image: np.ndarray,
                   titles: List[str]) -> np.ndarray:
        """
        为四宫格图像添加标题

        Args:
            image: 四宫格图像
            titles: 标题列表

        Returns:
            添加标题后的图像
        """
        h, w = image.shape[:2]
        title_height = 30

        # 创建带标题的图像
        result = np.zeros((h + title_height, w, 3), dtype=np.uint8)
        result[title_height:, :] = image

        # 添加标题
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.6
        thickness = 1
        color = (255, 255, 255)

        # 计算每个子图的位置
        sub_w = w // 2
        positions = [
            (10, 20),                    # 左上
            (sub_w + 10, 20),           # 右上
            (10, h // 2 + title_height + 20),     # 左下
            (sub_w + 10, h // 2 + title_height + 20)  # 右下
        ]

        for i, (title, pos) in enumerate(zip(titles, positions)):
            cv2.putText(result, title, pos, font, font_scale, color, thickness)

        return result

    def create_step_by_step_view(self,
                               original_image: np.ndarray,
                               lane_mask: 'LaneMask',
                               occlusion_analyses: List['OcclusionAnalysis'],
                               inference_results: List['InferenceResult']) -> np.ndarray:
        """
        创建逐步处理视图

        Args:
            original_image: 原始图像
            lane_mask: 标线mask对象
            occlusion_analyses: 遮挡分析结果
            inference_results: 推理结果

        Returns:
            逐步处理视图
        """
        steps = []

        # 步骤1：原始图像 + 标线
        original_lanes = lane_mask.get_combined_mask()
        step1 = self._overlay_mask_on_image(original_image, original_lanes,
                                          self.colors['original'])
        steps.append(("1. Original Lanes", step1))

        # 步骤2：遮挡检测
        occlusion_mask = lane_mask.get_occluded_mask()
        step2 = self._overlay_mask_on_image(step1, occlusion_mask,
                                          self.colors['occluded'])
        steps.append(("2. Occlusion Detection", step2))

        # 步骤3：逐个显示推理结果
        cumulative_completion = np.zeros_like(original_lanes)
        for i, result in enumerate(inference_results):
            cumulative_completion = np.logical_or(cumulative_completion,
                                                result.completed_mask)
            step = self._overlay_mask_on_image(original_image, original_lanes,
                                             self.colors['original'])
            step = self._overlay_mask_on_image(step, cumulative_completion,
                                             self.colors['completed'])
            steps.append((f"3.{i+1} Rule {result.rule_id}", step))

        # 创建网格布局
        return self._create_grid_layout(steps)

    def _create_grid_layout(self,
                           steps: List[Tuple[str, np.ndarray]],
                           cols: int = 3) -> np.ndarray:
        """
        创建网格布局

        Args:
            steps: (标题, 图像) 列表
            cols: 列数

        Returns:
            网格布局图像
        """
        if not steps:
            return np.zeros((100, 100, 3), dtype=np.uint8)

        # 计算行数
        rows = (len(steps) + cols - 1) // cols

        # 获取单个图像尺寸
        h, w = steps[0][1].shape[:2]
        title_height = 30

        # 创建结果图像
        result_h = rows * (h + title_height)
        result_w = cols * w
        result = np.zeros((result_h, result_w, 3), dtype=np.uint8)

        # 填充图像
        for i, (title, img) in enumerate(steps):
            row = i // cols
            col = i % cols

            y_start = row * (h + title_height)
            y_end = y_start + h
            x_start = col * w
            x_end = x_start + w

            # 放置图像
            result[y_start + title_height:y_end + title_height, x_start:x_end] = img

            # 添加标题
            cv2.putText(result, title, (x_start + 10, y_start + 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        return result

    def create_statistics_plot(self, statistics: Dict) -> np.ndarray:
        """
        创建统计图表

        Args:
            statistics: 统计数据

        Returns:
            统计图表图像
        """
        # 创建matplotlib图表
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        fig.suptitle('Occlusion Recovery Statistics')

        # 成功率饼图
        success_data = [
            statistics.get('successful_completions', 0),
            statistics.get('failed_completions', 0)
        ]
        # 避免全零数据导致的NaN问题
        if sum(success_data) > 0:
            axes[0, 0].pie(success_data, labels=['Success', 'Failed'], autopct='%1.1f%%')
        else:
            axes[0, 0].text(0.5, 0.5, 'No Data', ha='center', va='center', transform=axes[0, 0].transAxes)
        axes[0, 0].set_title('Completion Success Rate')

        # 按类型统计柱状图
        completion_by_type = statistics.get('completion_by_type', {})
        if completion_by_type:
            types = list(completion_by_type.keys())
            counts = list(completion_by_type.values())
            axes[0, 1].bar(types, counts)
            axes[0, 1].set_title('Completions by Lane Type')
            axes[0, 1].tick_params(axis='x', rotation=45)

        # 置信度分布直方图
        avg_confidence = statistics.get('average_confidence', 0)
        axes[1, 0].bar(['Average Confidence'], [avg_confidence])
        axes[1, 0].set_ylim(0, 1)
        axes[1, 0].set_title('Average Confidence')

        # 像素统计
        total_pixels = statistics.get('total_completed_pixels', 0)
        axes[1, 1].bar(['Completed Pixels'], [total_pixels])
        axes[1, 1].set_title('Total Completed Pixels')

        # 转换为图像
        plt.tight_layout()
        fig.canvas.draw()

        # 获取图像数据
        buf = np.frombuffer(fig.canvas.tostring_rgb(), dtype=np.uint8)
        buf = buf.reshape(fig.canvas.get_width_height()[::-1] + (3,))

        # 转换为BGR
        result = cv2.cvtColor(buf, cv2.COLOR_RGB2BGR)

        plt.close(fig)
        return result

    def save_visualization(self,
                          image: np.ndarray,
                          output_path: str,
                          quality: int = 95):
        """
        保存可视化结果

        Args:
            image: 图像
            output_path: 输出路径
            quality: JPEG质量
        """
        try:
            if output_path.lower().endswith('.jpg') or output_path.lower().endswith('.jpeg'):
                cv2.imwrite(output_path, image, [cv2.IMWRITE_JPEG_QUALITY, quality])
            else:
                cv2.imwrite(output_path, image)

            self.logger.info(f"Visualization saved to {output_path}")

        except Exception as e:
            self.logger.error(f"Failed to save visualization to {output_path}: {e}")
            raise
