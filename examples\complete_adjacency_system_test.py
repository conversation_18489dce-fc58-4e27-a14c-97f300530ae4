"""
完整的邻接补全系统测试
结合邻接检测、高级规则推理和结构补全
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import cv2
import json
from src.models.mask_model import LaneM<PERSON>, LaneType, OcclusionType
from src.core.adjacency_detector import AdjacencyDetector
from src.models.advanced_rule_set import create_advanced_rule_set
from src.core.structure_completer import StructureCompleter
from src.utils.mask_processor import MaskProcessor


def run_complete_adjacency_system_test():
    """运行完整的邻接补全系统测试"""
    print("=== 完整邻接补全系统测试 ===")
    
    # 1. 加载真实数据
    print("1. 加载真实数据...")
    image_path = "data/images/DJI_20220915153843_0320.JPG"
    mask_path = "data/masks/DJI_20220915153843_0320.png"
    
    try:
        image = cv2.imread(image_path)
        mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
        
        if image is None or mask is None:
            print("无法加载数据文件")
            return None
        
        # 缩放数据
        scale_factor = 0.25
        image = cv2.resize(image, None, fx=scale_factor, fy=scale_factor)
        mask = cv2.resize(mask, None, fx=scale_factor, fy=scale_factor, interpolation=cv2.INTER_NEAREST)
        
        print(f"   缩放后图像尺寸: {image.shape}")
        print(f"   Mask像素值: {np.unique(mask)}")
        
    except Exception as e:
        print(f"   加载数据时出错: {e}")
        return None
    
    # 2. 解析标线
    print("2. 解析标线数据...")
    processor = MaskProcessor()
    lane_masks, lane_types, _, _ = processor.parse_grayscale_mask(mask)
    
    print(f"   检测到 {len(lane_masks)} 种标线类型:")
    for name, lane_type in lane_types.items():
        pixels = np.sum(lane_masks[name])
        print(f"   - {name}: {lane_type.value} ({pixels} 像素)")
    
    # 3. 创建标线mask对象
    print("3. 创建标线mask对象...")
    lane_mask = LaneMask(image.shape[:2])
    for name, lane_mask_data in lane_masks.items():
        lane_type = lane_types[name]
        lane_id = lane_mask.add_lane_segment(lane_type, lane_mask_data)
        print(f"   添加标线段: {name} (ID: {lane_id})")
    
    # 4. 检测遮挡物和邻接关系
    print("4. 检测遮挡物和邻接关系...")
    detector = AdjacencyDetector({
        'max_gap_distance': 20,
        'min_adjacency_length': 15,
        'confidence_threshold': 0.3
    })
    
    # 检测遮挡物
    occlusion_masks, occlusion_types = detector.detect_occlusions_from_mask(mask)
    print(f"   检测到 {len(occlusion_masks)} 种遮挡物:")
    for name, occlusion_mask in occlusion_masks.items():
        pixels = np.sum(occlusion_mask)
        print(f"   - {name}: {occlusion_types[name].value} ({pixels} 像素)")
    
    if len(occlusion_masks) == 0:
        print("   没有检测到遮挡物，无法进行补全测试")
        return None
    
    # 检测邻接关系
    adjacency_analyses = detector.detect_adjacencies(lane_mask, occlusion_masks, occlusion_types)
    print(f"   检测到 {len(adjacency_analyses)} 个邻接关系")
    
    # 过滤显著邻接
    significant_adjacencies = detector.filter_significant_adjacencies(adjacency_analyses)
    print(f"   其中 {len(significant_adjacencies)} 个为显著邻接")
    
    for analysis in significant_adjacencies:
        lane_segment = lane_mask.lane_segments[analysis.lane_id]
        print(f"   - 标线 {analysis.lane_id} ({lane_segment.lane_type.value}) "
              f"与遮挡物 {analysis.occlusion_id} 邻接")
        print(f"     邻接比例: {analysis.adjacency_ratio:.2%}, "
              f"置信度: {analysis.confidence:.3f}")
    
    # 5. 创建上下文信息
    print("5. 创建推理上下文...")
    contexts = []
    for analysis in significant_adjacencies:
        lane_segment = lane_mask.lane_segments[analysis.lane_id]
        context = detector.create_adjacency_context(lane_segment, analysis, lane_mask)
        contexts.append(context)
        print(f"   标线 {analysis.lane_id}: 类型={context['lane_type'].value}, "
              f"邻接比例={context['adjacency_ratio']:.2%}, "
              f"可见长度={context['visible_length']}")
    
    # 6. 高级规则推理
    print("6. 执行高级规则推理...")
    rule_set = create_advanced_rule_set()
    inference_results = []
    
    for i, context in enumerate(contexts):
        matching_rules = rule_set.find_matching_rules(context)
        print(f"   上下文 {i+1} 匹配规则数: {len(matching_rules)}")
        
        for rule, confidence in matching_rules[:1]:  # 只取最佳规则
            print(f"   - 规则 {rule.rule_id}: {rule.name} (置信度: {confidence:.3f})")
            
            # 执行补全动作
            if len(rule.actions) > 0:
                action = rule.actions[0]
                analysis = significant_adjacencies[i]
                lane_segment = lane_mask.lane_segments[analysis.lane_id]
                occlusion_region = lane_mask.occlusion_regions[analysis.occlusion_id]
                
                try:
                    completed_mask = action.execute(lane_segment, occlusion_region, context)
                    print(f"     补全结果: {np.sum(completed_mask)} 像素")
                    
                    # 创建推理结果（模拟InferenceResult结构）
                    inference_result = type('InferenceResult', (), {
                        'lane_id': analysis.lane_id,
                        'occlusion_id': analysis.occlusion_id,
                        'rule_id': rule.rule_id,
                        'completed_mask': completed_mask,
                        'confidence': confidence,
                        'rule_confidence': confidence,
                        'context': context
                    })()
                    
                    inference_results.append(inference_result)
                    
                except Exception as e:
                    print(f"     补全执行失败: {e}")
    
    # 7. 结构补全
    print("7. 执行结构补全...")
    completer = StructureCompleter()
    
    if len(inference_results) > 0:
        # 暂时禁用冲突检测以便测试
        completion_result = completer.complete_structure(lane_mask, inference_results)
        
        print("   补全统计:")
        stats = completion_result.statistics
        print(f"   - 总推理数: {stats['total_inferences']}")
        print(f"   - 成功补全: {stats['successful_completions']}")
        print(f"   - 失败补全: {stats['failed_completions']}")
        print(f"   - 补全像素: {stats['total_completed_pixels']}")
        if stats['successful_completions'] > 0:
            print(f"   - 平均置信度: {stats['average_confidence']:.3f}")
            print(f"   - 按类型补全: {stats['completion_by_type']}")
    else:
        print("   没有推理结果可供补全")
        completion_result = None
    
    # 8. 生成可视化
    print("8. 生成可视化结果...")
    output_dir = "examples/output_complete_adjacency"
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存原始数据
    cv2.imwrite(f"{output_dir}/scaled_image.jpg", image)
    cv2.imwrite(f"{output_dir}/original_mask.png", mask)
    
    # 保存遮挡物
    for name, occlusion_mask in occlusion_masks.items():
        cv2.imwrite(f"{output_dir}/occlusion_{name}.png", occlusion_mask * 255)
    
    # 保存标线
    for name, lane_mask_data in lane_masks.items():
        cv2.imwrite(f"{output_dir}/lane_{name}.png", lane_mask_data * 255)
    
    # 创建邻接关系可视化
    adjacency_vis = create_comprehensive_visualization(
        image, mask, lane_mask, occlusion_masks, significant_adjacencies, 
        inference_results, completion_result
    )
    cv2.imwrite(f"{output_dir}/comprehensive_visualization.jpg", adjacency_vis)
    
    # 保存推理结果
    if len(inference_results) > 0:
        for i, result in enumerate(inference_results):
            cv2.imwrite(f"{output_dir}/inference_result_{i}_{result.rule_id}.png", 
                       result.completed_mask * 255)
    
    # 保存补全结果
    if completion_result is not None:
        cv2.imwrite(f"{output_dir}/final_completion.png", 
                   completion_result.completed_mask * 255)
        cv2.imwrite(f"{output_dir}/completion_regions.png", 
                   completion_result.completion_regions * 255)
    
    # 9. 生成详细报告
    print("9. 生成详细报告...")
    report = generate_comprehensive_report(
        lane_masks, lane_types, occlusion_masks, occlusion_types,
        significant_adjacencies, inference_results, completion_result
    )
    
    with open(f"{output_dir}/comprehensive_report.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"   结果已保存到: {output_dir}")
    
    # 10. 显示总结
    print("\n=== 完整邻接补全系统总结 ===")
    print(f"数据信息:")
    print(f"  - 图像尺寸: {image.shape}")
    print(f"  - 标线类型: {len(lane_types)}")
    print(f"  - 遮挡物类型: {len(occlusion_masks)}")
    print(f"  - 邻接关系: {len(adjacency_analyses)}")
    print(f"  - 显著邻接: {len(significant_adjacencies)}")
    
    print(f"处理结果:")
    print(f"  - 匹配规则: {len(inference_results)}")
    if completion_result:
        print(f"  - 成功补全: {completion_result.statistics['successful_completions']}")
        print(f"  - 补全像素: {completion_result.statistics['total_completed_pixels']}")
    
    print("\n完整邻接补全系统测试完成！")
    return completion_result


def create_comprehensive_visualization(image, mask, lane_mask, occlusion_masks, 
                                     adjacencies, inference_results, completion_result):
    """创建综合可视化"""
    h, w = image.shape[:2]
    
    # 创建3x2布局
    result = np.zeros((h * 2, w * 3, 3), dtype=np.uint8)
    
    # 第一行：原图、标线、遮挡物
    result[:h, :w] = image
    
    # 标线可视化
    lane_vis = np.zeros((h, w, 3), dtype=np.uint8)
    for lane_segment in lane_mask.lane_segments.values():
        lane_vis[lane_segment.mask > 0] = [255, 255, 255]
    result[:h, w:w*2] = lane_vis
    
    # 遮挡物可视化
    occlusion_vis = np.zeros((h, w, 3), dtype=np.uint8)
    colors = {'car': [255, 0, 0], 'human': [0, 255, 0], 'clatter': [0, 0, 255], 'obstacle': [255, 255, 0]}
    for name, occlusion_mask in occlusion_masks.items():
        color = colors.get(name, [128, 128, 128])
        occlusion_vis[occlusion_mask > 0] = color
    result[:h, w*2:] = occlusion_vis
    
    # 第二行：邻接关系、推理结果、最终补全
    # 邻接关系
    adjacency_vis = lane_vis.copy()
    for name, occlusion_mask in occlusion_masks.items():
        color = colors.get(name, [128, 128, 128])
        adjacency_vis[occlusion_mask > 0] = color
    
    # 标记邻接点
    for adjacency in adjacencies:
        for point in adjacency.contact_points[:10]:  # 只显示前10个点
            x, y = point
            if 0 <= x < w and 0 <= y < h:
                cv2.circle(adjacency_vis, (x, y), 2, (255, 0, 255), -1)
    
    result[h:, :w] = adjacency_vis
    
    # 推理结果
    inference_vis = np.zeros((h, w, 3), dtype=np.uint8)
    for i, inference_result in enumerate(inference_results):
        color = [(i * 50) % 255, (i * 100) % 255, (i * 150) % 255]
        inference_vis[inference_result.completed_mask > 0] = color
    result[h:, w:w*2] = inference_vis
    
    # 最终补全
    final_vis = lane_vis.copy()
    if completion_result is not None:
        final_vis[completion_result.completion_regions > 0] = [0, 255, 0]  # 绿色补全
    result[h:, w*2:] = final_vis
    
    # 添加标题
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(result, "Original", (10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Lanes", (w + 10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Occlusions", (w*2 + 10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Adjacencies", (10, h + 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Inferences", (w + 10, h + 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(result, "Final Result", (w*2 + 10, h + 25), font, 0.7, (255, 255, 255), 2)
    
    return result


def generate_comprehensive_report(lane_masks, lane_types, occlusion_masks, occlusion_types,
                                adjacencies, inference_results, completion_result):
    """生成综合报告"""
    report = {
        'system_info': {
            'detection_method': 'adjacency_based',
            'rule_system': 'advanced_rules',
            'completion_method': 'structure_completer'
        },
        'data_analysis': {
            'lane_types': {name: lane_type.value for name, lane_type in lane_types.items()},
            'lane_pixels': {name: int(np.sum(mask)) for name, mask in lane_masks.items()},
            'occlusion_types': {name: occ_type.value for name, occ_type in occlusion_types.items()},
            'occlusion_pixels': {name: int(np.sum(mask)) for name, mask in occlusion_masks.items()}
        },
        'adjacency_analysis': [
            {
                'lane_id': adj.lane_id,
                'occlusion_id': adj.occlusion_id,
                'occlusion_type': adj.occlusion_type.value,
                'adjacency_ratio': adj.adjacency_ratio,
                'adjacency_length': adj.adjacency_length,
                'gap_distance': adj.gap_distance,
                'confidence': adj.confidence,
                'contact_points_count': len(adj.contact_points)
            }
            for adj in adjacencies
        ],
        'inference_results': [
            {
                'rule_id': result.rule_id,
                'lane_id': result.lane_id,
                'occlusion_id': result.occlusion_id,
                'confidence': result.confidence,
                'completed_pixels': int(np.sum(result.completed_mask))
            }
            for result in inference_results
        ],
        'completion_statistics': completion_result.statistics if completion_result else {}
    }
    
    return report


if __name__ == "__main__":
    try:
        completion_result = run_complete_adjacency_system_test()
        if completion_result is not None:
            print("\n系统测试运行成功！")
        
    except Exception as e:
        print(f"系统测试运行出错: {e}")
        import traceback
        traceback.print_exc()
