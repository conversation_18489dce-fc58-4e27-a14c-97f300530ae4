"""
修正的补全动作
解决邻接检测与补全逻辑的问题
"""

import numpy as np
import cv2
from typing import Dict, List, Tuple, Optional, Any

from .mask_model import LaneSegment, OcclusionRegion


class FixedCompletionAction:
    """修正的补全动作"""
    
    def __init__(self, action_type: str, parameters: Dict[str, Any]):
        self.action_type = action_type
        self.parameters = parameters
    
    def execute(self, 
                lane_segment: LaneSegment,
                occlusion_region: OcclusionRegion,
                context: Dict[str, Any]) -> np.ndarray:
        """执行补全动作"""
        
        if self.action_type == "solid_line_completion":
            return self._complete_solid_line_fixed(lane_segment, occlusion_region, context)
        elif self.action_type == "dashed_line_completion":
            return self._complete_dashed_line_fixed(lane_segment, occlusion_region, context)
        elif self.action_type == "stop_line_completion":
            return self._complete_stop_line_fixed(lane_segment, occlusion_region, context)
        else:
            return np.zeros_like(occlusion_region.mask)
    
    def _complete_solid_line_fixed(self, 
                                 lane_segment: LaneSegment,
                                 occlusion_region: OcclusionRegion,
                                 context: Dict[str, Any]) -> np.ndarray:
        """
        修正的实线补全算法
        基于邻接关系，在遮挡物区域内补全被遮挡的标线
        """
        result_mask = np.zeros_like(occlusion_region.mask)
        
        print(f"开始实线补全: 标线像素={np.sum(lane_segment.mask)}, 遮挡像素={np.sum(occlusion_region.mask)}")
        
        # 1. 分析标线的几何属性
        lane_props = self._analyze_lane_geometry(lane_segment.mask)
        if lane_props is None:
            print("无法分析标线几何属性")
            return result_mask
        
        print(f"标线属性: 宽度={lane_props['width']:.1f}, 方向={lane_props['direction']}")
        
        # 2. 找到标线与遮挡物的接触边界
        contact_boundary = self._find_contact_boundary(lane_segment.mask, occlusion_region.mask)
        if len(contact_boundary) == 0:
            print("未找到接触边界")
            return result_mask
        
        print(f"找到接触边界: {len(contact_boundary)} 个点")
        
        # 3. 基于接触边界和标线方向，在遮挡区域内生成补全
        result_mask = self._generate_line_completion_in_occlusion(
            occlusion_region.mask, contact_boundary, lane_props
        )
        
        print(f"生成补全结果: {np.sum(result_mask)} 像素")
        return result_mask
    
    def _complete_dashed_line_fixed(self,
                                  lane_segment: LaneSegment,
                                  occlusion_region: OcclusionRegion,
                                  context: Dict[str, Any]) -> np.ndarray:
        """修正的虚线补全算法"""
        result_mask = np.zeros_like(occlusion_region.mask)
        
        print(f"开始虚线补全: 标线像素={np.sum(lane_segment.mask)}, 遮挡像素={np.sum(occlusion_region.mask)}")
        
        # 1. 分析虚线模式
        dashed_props = self._analyze_dashed_pattern_simple(lane_segment.mask)
        if dashed_props is None:
            print("无法分析虚线模式")
            return result_mask
        
        print(f"虚线属性: 段长={dashed_props['dash_length']:.1f}, 间隔={dashed_props['gap_length']:.1f}")
        
        # 2. 找到接触边界
        contact_boundary = self._find_contact_boundary(lane_segment.mask, occlusion_region.mask)
        if len(contact_boundary) == 0:
            print("未找到接触边界")
            return result_mask
        
        # 3. 在遮挡区域内生成虚线模式
        result_mask = self._generate_dashed_completion_in_occlusion(
            occlusion_region.mask, contact_boundary, dashed_props
        )
        
        print(f"生成虚线补全结果: {np.sum(result_mask)} 像素")
        return result_mask
    
    def _complete_stop_line_fixed(self,
                                lane_segment: LaneSegment,
                                occlusion_region: OcclusionRegion,
                                context: Dict[str, Any]) -> np.ndarray:
        """修正的停止线补全算法"""
        # 停止线通常是垂直的，可以使用类似实线的方法
        return self._complete_solid_line_fixed(lane_segment, occlusion_region, context)
    
    def _analyze_lane_geometry(self, lane_mask: np.ndarray) -> Optional[Dict[str, Any]]:
        """分析标线的几何属性"""
        if not np.any(lane_mask):
            return None
        
        # 1. 计算标线的主方向
        coords = np.where(lane_mask)
        if len(coords[0]) < 10:
            return None
        
        # 使用PCA计算主方向
        points = np.column_stack((coords[1], coords[0]))  # (x, y)
        mean_point = np.mean(points, axis=0)
        centered_points = points - mean_point
        
        # 计算协方差矩阵
        cov_matrix = np.cov(centered_points.T)
        eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)
        
        # 主方向是最大特征值对应的特征向量
        main_direction = eigenvectors[:, np.argmax(eigenvalues)]
        
        # 2. 计算标线宽度
        # 使用形态学操作估计宽度
        contours, _ = cv2.findContours(lane_mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if len(contours) == 0:
            return None
        
        main_contour = max(contours, key=cv2.contourArea)
        rect = cv2.minAreaRect(main_contour)
        width = min(rect[1])  # 较小的边作为宽度
        
        return {
            'direction': main_direction,
            'width': max(3, width),  # 最小宽度为3像素
            'center': mean_point,
            'length': max(rect[1])
        }
    
    def _find_contact_boundary(self, lane_mask: np.ndarray, occlusion_mask: np.ndarray) -> List[Tuple[int, int]]:
        """找到标线与遮挡物的接触边界"""
        # 1. 对标线进行膨胀，找到可能的接触区域
        kernel = np.ones((5, 5), np.uint8)
        dilated_lane = cv2.dilate(lane_mask.astype(np.uint8), kernel, iterations=1)
        
        # 2. 找到膨胀后的标线与遮挡物的交集
        contact_region = np.logical_and(dilated_lane, occlusion_mask)
        
        # 3. 提取接触边界点
        contact_coords = np.where(contact_region)
        contact_points = list(zip(contact_coords[1], contact_coords[0]))  # (x, y)
        
        return contact_points
    
    def _generate_line_completion_in_occlusion(self,
                                             occlusion_mask: np.ndarray,
                                             contact_boundary: List[Tuple[int, int]],
                                             lane_props: Dict[str, Any]) -> np.ndarray:
        """在遮挡区域内生成线段补全"""
        result = np.zeros_like(occlusion_mask)
        
        if len(contact_boundary) == 0:
            return result
        
        # 1. 计算接触边界的中心和方向
        contact_points = np.array(contact_boundary)
        contact_center = np.mean(contact_points, axis=0)
        
        # 2. 沿着标线方向，在遮挡区域内绘制线段
        direction = lane_props['direction']
        width = int(lane_props['width'])
        
        # 3. 找到遮挡区域的边界
        occlusion_coords = np.where(occlusion_mask)
        if len(occlusion_coords[0]) == 0:
            return result
        
        y_min, y_max = np.min(occlusion_coords[0]), np.max(occlusion_coords[0])
        x_min, x_max = np.min(occlusion_coords[1]), np.max(occlusion_coords[1])
        
        # 4. 计算线段的起点和终点
        # 沿着方向向量延伸
        length = max(x_max - x_min, y_max - y_min)
        
        start_point = contact_center - direction * length / 2
        end_point = contact_center + direction * length / 2
        
        # 确保点在图像范围内
        start_point = np.clip(start_point, [0, 0], [occlusion_mask.shape[1]-1, occlusion_mask.shape[0]-1])
        end_point = np.clip(end_point, [0, 0], [occlusion_mask.shape[1]-1, occlusion_mask.shape[0]-1])
        
        # 5. 绘制线段
        cv2.line(result, 
                tuple(start_point.astype(int)), 
                tuple(end_point.astype(int)), 
                1, width)
        
        # 6. 只保留在遮挡区域内的部分
        result = np.logical_and(result, occlusion_mask).astype(np.uint8)
        
        return result
    
    def _analyze_dashed_pattern_simple(self, lane_mask: np.ndarray) -> Optional[Dict[str, Any]]:
        """简化的虚线模式分析"""
        if not np.any(lane_mask):
            return None
        
        # 1. 获取连通组件
        num_labels, labels = cv2.connectedComponents(lane_mask.astype(np.uint8))
        if num_labels < 2:
            return None
        
        # 2. 分析各个虚线段
        dash_lengths = []
        dash_centers = []
        
        for label in range(1, num_labels):
            segment_mask = (labels == label)
            if np.sum(segment_mask) < 5:  # 忽略太小的段
                continue
            
            # 计算段的属性
            coords = np.where(segment_mask)
            center = (np.mean(coords[1]), np.mean(coords[0]))  # (x, y)
            
            # 估计段长度
            contours, _ = cv2.findContours(segment_mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            if len(contours) > 0:
                rect = cv2.minAreaRect(contours[0])
                length = max(rect[1])
                dash_lengths.append(length)
                dash_centers.append(center)
        
        if len(dash_lengths) < 2:
            return None
        
        # 3. 计算平均属性
        avg_dash_length = np.mean(dash_lengths)
        
        # 计算间隔
        if len(dash_centers) >= 2:
            distances = []
            for i in range(len(dash_centers) - 1):
                dist = np.sqrt((dash_centers[i+1][0] - dash_centers[i][0])**2 + 
                              (dash_centers[i+1][1] - dash_centers[i][1])**2)
                distances.append(dist)
            avg_gap = np.mean(distances) - avg_dash_length
        else:
            avg_gap = avg_dash_length
        
        return {
            'dash_length': max(10, avg_dash_length),
            'gap_length': max(10, avg_gap),
            'width': 3,
            'centers': dash_centers
        }
    
    def _generate_dashed_completion_in_occlusion(self,
                                               occlusion_mask: np.ndarray,
                                               contact_boundary: List[Tuple[int, int]],
                                               dashed_props: Dict[str, Any]) -> np.ndarray:
        """在遮挡区域内生成虚线补全"""
        result = np.zeros_like(occlusion_mask)
        
        if len(contact_boundary) == 0:
            return result
        
        # 1. 计算接触边界的中心
        contact_points = np.array(contact_boundary)
        contact_center = np.mean(contact_points, axis=0)
        
        # 2. 估计虚线方向（简化：使用水平方向）
        occlusion_coords = np.where(occlusion_mask)
        if len(occlusion_coords[0]) == 0:
            return result
        
        x_min, x_max = np.min(occlusion_coords[1]), np.max(occlusion_coords[1])
        y_center = int(contact_center[1])
        
        # 3. 沿着方向绘制虚线段
        dash_length = int(dashed_props['dash_length'])
        gap_length = int(dashed_props['gap_length'])
        width = int(dashed_props['width'])
        
        current_x = x_min
        is_dash = True
        
        while current_x < x_max:
            if is_dash:
                # 绘制虚线段
                end_x = min(current_x + dash_length, x_max)
                
                if y_center >= 0 and y_center < result.shape[0]:
                    cv2.line(result, (current_x, y_center), (end_x, y_center), 1, width)
                
                current_x = end_x
            else:
                # 跳过间隔
                current_x += gap_length
            
            is_dash = not is_dash
        
        # 4. 只保留在遮挡区域内的部分
        result = np.logical_and(result, occlusion_mask).astype(np.uint8)
        
        return result
