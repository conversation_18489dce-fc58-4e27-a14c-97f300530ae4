"""
真实数据测试
使用您提供的真实UAV图像和mask数据进行测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import cv2
from src.models.mask_model import LaneMask, LaneType, OcclusionType
from src.core.occlusion_detector import OcclusionDetector
from src.core.rule_engine import RuleEngine
from src.core.structure_completer import StructureCompleter
from src.utils.mask_processor import MaskProcessor


def create_synthetic_occlusion(original_mask, occlusion_ratio=0.3):
    """
    在真实数据上创建合成遮挡

    Args:
        original_mask: 原始标线mask
        occlusion_ratio: 遮挡比例

    Returns:
        (occluded_mask, occlusion_masks, occlusion_types)
    """
    height, width = original_mask.shape

    # 创建遮挡后的mask
    occluded_mask = original_mask.copy()

    # 创建遮挡区域
    occlusion_masks = {}
    occlusion_types = {}

    # 找到有标线的区域
    lane_pixels = np.where(original_mask > 0)
    if len(lane_pixels[0]) == 0:
        return occluded_mask, occlusion_masks, occlusion_types

    # 随机选择一些区域作为车辆遮挡
    num_cars = 3
    for i in range(num_cars):
        # 随机选择遮挡中心
        idx = np.random.randint(0, len(lane_pixels[0]))
        center_y = lane_pixels[0][idx]
        center_x = lane_pixels[1][idx]

        # 创建矩形遮挡区域（模拟车辆）
        car_width = np.random.randint(80, 150)
        car_height = np.random.randint(40, 80)

        y1 = max(0, center_y - car_height // 2)
        y2 = min(height, center_y + car_height // 2)
        x1 = max(0, center_x - car_width // 2)
        x2 = min(width, center_x + car_width // 2)

        # 创建车辆遮挡mask
        car_mask = np.zeros((height, width), dtype=np.uint8)
        car_mask[y1:y2, x1:x2] = 1

        # 只保留与标线重合的部分
        car_mask = np.logical_and(car_mask, original_mask > 0).astype(np.uint8)

        if np.sum(car_mask) > 100:  # 只保留足够大的遮挡
            occlusion_masks[f'car_{i}'] = car_mask
            occlusion_types[f'car_{i}'] = OcclusionType.DYNAMIC

            # 从原始mask中移除被遮挡的部分
            occluded_mask[car_mask > 0] = 0

    # 添加一些行人遮挡（椭圆形）
    num_humans = 2
    for i in range(num_humans):
        # 随机选择遮挡中心
        idx = np.random.randint(0, len(lane_pixels[0]))
        center_y = lane_pixels[0][idx]
        center_x = lane_pixels[1][idx]

        # 创建椭圆遮挡区域（模拟行人）
        human_mask = np.zeros((height, width), dtype=np.uint8)
        axes = (np.random.randint(15, 30), np.random.randint(25, 45))
        cv2.ellipse(human_mask, (center_x, center_y), axes, 0, 0, 360, 1, -1)

        # 只保留与标线重合的部分
        human_mask = np.logical_and(human_mask, original_mask > 0).astype(np.uint8)

        if np.sum(human_mask) > 50:  # 只保留足够大的遮挡
            occlusion_masks[f'human_{i}'] = human_mask
            occlusion_types[f'human_{i}'] = OcclusionType.DYNAMIC

            # 从原始mask中移除被遮挡的部分
            occluded_mask[human_mask > 0] = 0

    return occluded_mask, occlusion_masks, occlusion_types


def run_real_data_test():
    """运行真实数据测试"""
    print("=== 真实数据测试 ===")

    # 1. 加载真实数据
    print("1. 加载真实数据...")
    image_path = "data/images/DJI_20220915153843_0320.JPG"
    mask_path = "data/masks/DJI_20220915153843_0320.png"

    try:
        image = cv2.imread(image_path)
        original_mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)

        if image is None or original_mask is None:
            print(f"   错误：无法加载数据文件")
            print(f"   图像路径: {image_path}")
            print(f"   Mask路径: {mask_path}")
            return None, None

        print(f"   图像尺寸: {image.shape}")
        print(f"   Mask尺寸: {original_mask.shape}")
        print(f"   Mask像素值: {np.unique(original_mask)}")

    except Exception as e:
        print(f"   加载数据时出错: {e}")
        return None, None

    # 2. 创建合成遮挡
    print("2. 创建合成遮挡...")
    np.random.seed(42)  # 固定随机种子以便重现
    occluded_mask, occlusion_masks, occlusion_types = create_synthetic_occlusion(original_mask)

    print(f"   创建了 {len(occlusion_masks)} 个遮挡区域")
    for name, mask in occlusion_masks.items():
        print(f"   - {name}: {np.sum(mask)} 像素")

    # 3. 初始化处理器
    print("3. 初始化处理器...")
    mask_processor = MaskProcessor()
    detector = OcclusionDetector({'confidence_threshold': 0.2})  # 降低阈值
    engine = RuleEngine()
    completer = StructureCompleter()

    # 4. 解析原始标线（用于遮挡检测）
    print("4. 解析标线数据...")
    lane_masks, lane_types, _, _ = mask_processor.parse_grayscale_mask(original_mask)

    print(f"   检测到 {len(lane_masks)} 种标线类型:")
    for name, lane_type in lane_types.items():
        pixels = np.sum(lane_masks[name])
        print(f"   - {name}: {lane_type.value} ({pixels} 像素)")

    # 5. 创建标线mask对象（使用完整标线用于遮挡检测）
    print("5. 创建标线mask对象...")
    lane_mask = LaneMask(image.shape[:2])

    for name, mask in lane_masks.items():
        lane_type = lane_types[name]
        lane_id = lane_mask.add_lane_segment(lane_type, mask)
        print(f"   添加标线段: {name} (ID: {lane_id})")

    # 6. 检测遮挡
    print("6. 检测遮挡...")
    occlusion_analyses = detector.detect_occlusions(
        lane_mask, occlusion_masks, occlusion_types
    )

    print(f"   检测到 {len(occlusion_analyses)} 个遮挡区域")
    for analysis in occlusion_analyses:
        lane_segment = lane_mask.lane_segments[analysis.lane_id]
        print(f"   - 标线 {analysis.lane_id} ({lane_segment.lane_type.value}) "
              f"被 {analysis.occlusion_type.value} 遮挡 {analysis.overlap_ratio:.2%}, "
              f"置信度: {analysis.confidence:.3f}")

    # 过滤显著遮挡
    significant_occlusions = detector.filter_significant_occlusions(occlusion_analyses)
    print(f"   其中 {len(significant_occlusions)} 个为显著遮挡")

    # 7. 创建上下文信息
    print("7. 创建推理上下文...")
    contexts = []
    for analysis in significant_occlusions:
        lane_segment = lane_mask.lane_segments[analysis.lane_id]
        context = detector.create_occlusion_context(lane_segment, analysis, lane_mask)
        contexts.append(context)
        print(f"   标线 {analysis.lane_id}: 类型={context['lane_type'].value}, "
              f"遮挡比例={context['occlusion_ratio']:.2%}, "
              f"可见长度={context['visible_length']}")

    # 8. 规则推理
    print("8. 执行规则推理...")
    inference_results = engine.infer_completions(lane_mask, significant_occlusions, contexts)

    print(f"   生成 {len(inference_results)} 个推理结果")
    for result in inference_results:
        lane_segment = lane_mask.lane_segments[result.lane_id]
        print(f"   - 规则 {result.rule_id}: 标线 {result.lane_id} ({lane_segment.lane_type.value}), "
              f"置信度: {result.confidence:.3f}")

    # 9. 结构补全
    print("9. 执行结构补全...")
    completion_result = completer.complete_structure(lane_mask, inference_results)

    print("   补全统计:")
    stats = completion_result.statistics
    print(f"   - 总推理数: {stats['total_inferences']}")
    print(f"   - 成功补全: {stats['successful_completions']}")
    print(f"   - 失败补全: {stats['failed_completions']}")
    print(f"   - 补全像素: {stats['total_completed_pixels']}")
    if stats['successful_completions'] > 0:
        print(f"   - 平均置信度: {stats['average_confidence']:.3f}")
        print(f"   - 按类型补全: {stats['completion_by_type']}")

    # 10. 生成可视化
    print("10. 生成可视化结果...")

    # 创建详细的RGB可视化
    rgb_detailed = mask_processor.create_detailed_rgb_visualization(
        lane_mask, completion_result.completed_mask, completion_result.completion_regions
    )

    # 创建对比图像（缩放以便查看）
    scale_factor = 0.2  # 缩放到20%以便查看
    small_image = cv2.resize(image, None, fx=scale_factor, fy=scale_factor)
    small_original = cv2.resize(completion_result.original_mask, None, fx=scale_factor, fy=scale_factor)
    small_completed = cv2.resize(completion_result.completed_mask, None, fx=scale_factor, fy=scale_factor)
    small_completion = cv2.resize(completion_result.completion_regions, None, fx=scale_factor, fy=scale_factor)

    comparison_image = mask_processor.create_comparison_image(
        small_image, small_original, small_completed, small_completion
    )

    # 11. 保存结果
    print("11. 保存结果...")
    output_dir = "examples/output_real_data"
    os.makedirs(output_dir, exist_ok=True)

    # 保存缩放后的图像（原图太大）
    cv2.imwrite(f"{output_dir}/original_image_small.jpg", small_image)
    cv2.imwrite(f"{output_dir}/original_mask.png", original_mask)
    cv2.imwrite(f"{output_dir}/occluded_mask.png", occluded_mask)
    cv2.imwrite(f"{output_dir}/comparison_view.jpg", comparison_image)

    # 保存RGB可视化（缩放版本）
    rgb_small = cv2.resize(rgb_detailed, None, fx=scale_factor, fy=scale_factor)
    cv2.imwrite(f"{output_dir}/rgb_visualization_small.jpg", rgb_small)

    # 保存遮挡区域
    for name, mask in occlusion_masks.items():
        cv2.imwrite(f"{output_dir}/occlusion_{name}.png", mask * 255)

    # 保存各个标线类型的mask（缩放版本）
    for name, mask in lane_masks.items():
        mask_small = cv2.resize(mask, None, fx=scale_factor, fy=scale_factor)
        cv2.imwrite(f"{output_dir}/lane_{name}_small.png", mask_small * 255)

    # 保存补全区域
    if np.any(completion_result.completion_regions):
        completion_small = cv2.resize(completion_result.completion_regions, None, fx=scale_factor, fy=scale_factor)
        cv2.imwrite(f"{output_dir}/completion_regions_small.png", completion_small * 255)

    # 生成详细报告
    report = completer.generate_completion_report(completion_result)

    # 添加真实数据信息
    report['real_data_info'] = {
        'image_path': image_path,
        'mask_path': mask_path,
        'original_image_size': image.shape,
        'original_mask_size': original_mask.shape,
        'unique_pixel_values': np.unique(original_mask).tolist(),
        'synthetic_occlusions': {name: int(np.sum(mask)) for name, mask in occlusion_masks.items()},
        'detected_lane_types': {name: lane_type.value for name, lane_type in lane_types.items()}
    }

    # 保存报告
    import json
    with open(f"{output_dir}/detailed_report.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False, default=str)

    print(f"   结果已保存到: {output_dir}")

    # 12. 显示总结
    print("\n=== 真实数据测试总结 ===")
    print(f"数据信息:")
    print(f"  - 原始图像尺寸: {image.shape}")
    print(f"  - 检测到标线类型: {len(lane_types)}")
    print(f"  - 合成遮挡区域: {len(occlusion_masks)}")
    print(f"  - 检测到的遮挡: {len(occlusion_analyses)}")
    print(f"  - 显著遮挡: {len(significant_occlusions)}")

    print(f"处理结果:")
    print(f"  - 匹配规则数: {len(inference_results)}")
    print(f"  - 成功补全: {stats['successful_completions']}")
    print(f"  - 补全像素: {stats['total_completed_pixels']}")

    if len(occlusion_analyses) > 0:
        print(f"遮挡详情:")
        for analysis in occlusion_analyses:
            lane_segment = lane_mask.lane_segments[analysis.lane_id]
            print(f"  - {lane_segment.lane_type.value}: {analysis.overlap_ratio:.1%} 被遮挡")

    print("\n真实数据测试完成！")
    print("注意：由于原图尺寸很大，保存的可视化结果已缩放到20%以便查看")

    return completion_result, report


if __name__ == "__main__":
    try:
        completion_result, report = run_real_data_test()
        if completion_result is not None:
            print("\n测试运行成功！")

    except Exception as e:
        print(f"测试运行出错: {e}")
        import traceback
        traceback.print_exc()
