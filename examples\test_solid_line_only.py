"""
专门测试实线补全
只关注solid_line的正确实现
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import cv2
from src.utils.mask_processor import MaskProcessor

def test_solid_line_only():
    """专门测试实线"""
    print("=== 专门测试实线补全 ===")
    
    # 1. 加载数据
    image_path = "data/images/DJI_20220915153843_0320.JPG"
    mask_path = "data/masks/DJI_20220915153843_0320.png"
    
    image = cv2.imread(image_path)
    mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
    
    # 缩放
    scale_factor = 0.25
    image = cv2.resize(image, None, fx=scale_factor, fy=scale_factor)
    mask = cv2.resize(mask, None, fx=scale_factor, fy=scale_factor, interpolation=cv2.INTER_NEAREST)
    
    print(f"图像尺寸: {image.shape}")
    
    # 2. 只提取实线
    processor = MaskProcessor()
    lane_masks, lane_types, _, _ = processor.parse_grayscale_mask(mask)
    
    # 找到实线
    solid_line_mask = None
    solid_line_name = None
    for name, lane_type in lane_types.items():
        if lane_type.value == 'solid_line':
            solid_line_mask = lane_masks[name]
            solid_line_name = name
            break
    
    if solid_line_mask is None:
        print("没有找到实线")
        return
    
    print(f"找到实线: {solid_line_name}")
    print(f"实线像素数: {np.sum(solid_line_mask):,}")
    
    # 3. 提取遮挡物
    occlusion_pixels = [1, 2, 11, 12]  # car, human, clatter, obstacle
    unified_occlusion_mask = np.zeros_like(mask)
    for pixel_value in occlusion_pixels:
        unified_occlusion_mask[mask == pixel_value] = 1
    
    print(f"遮挡物像素数: {np.sum(unified_occlusion_mask):,}")
    
    # 4. 检查实线与遮挡物的关系
    # 直接重叠
    direct_overlap = np.logical_and(solid_line_mask, unified_occlusion_mask)
    overlap_pixels = np.sum(direct_overlap)
    
    # 邻接关系
    kernel = np.ones((20, 20), np.uint8)  # 20像素邻接范围
    dilated_solid_line = cv2.dilate(solid_line_mask.astype(np.uint8), kernel, iterations=1)
    adjacency = np.logical_and(dilated_solid_line, unified_occlusion_mask)
    adjacency_pixels = np.sum(adjacency)
    
    print(f"实线与遮挡物关系:")
    print(f"  直接重叠: {overlap_pixels:,} 像素")
    print(f"  邻接关系: {adjacency_pixels:,} 像素")
    
    # 5. 创建可视化
    output_dir = "examples/output_solid_line_only"
    os.makedirs(output_dir, exist_ok=True)
    
    h, w = image.shape[:2]
    
    # 创建2x2可视化
    vis = np.zeros((h * 2, w * 2, 3), dtype=np.uint8)
    
    # 左上：原图
    vis[:h, :w] = image
    
    # 右上：实线（白色）
    solid_line_vis = np.zeros((h, w, 3), dtype=np.uint8)
    solid_line_vis[solid_line_mask > 0] = [255, 255, 255]
    vis[:h, w:] = solid_line_vis
    
    # 左下：遮挡物（红色）
    occlusion_vis = np.zeros((h, w, 3), dtype=np.uint8)
    occlusion_vis[unified_occlusion_mask > 0] = [255, 0, 0]
    vis[h:, :w] = occlusion_vis
    
    # 右下：实线+遮挡物+邻接关系
    combined_vis = solid_line_vis.copy()
    combined_vis[unified_occlusion_mask > 0] = [255, 0, 0]  # 红色遮挡物
    combined_vis[adjacency > 0] = [0, 255, 255]  # 青色邻接区域
    combined_vis[direct_overlap > 0] = [0, 255, 0]  # 绿色重叠区域
    vis[h:, w:] = combined_vis
    
    # 添加标题
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(vis, "Original Image", (10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(vis, "Solid Line", (w + 10, 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(vis, "Occlusions", (10, h + 25), font, 0.7, (255, 255, 255), 2)
    cv2.putText(vis, "Relationships", (w + 10, h + 25), font, 0.7, (255, 255, 255), 2)
    
    cv2.imwrite(f"{output_dir}/solid_line_analysis.jpg", vis)
    
    # 6. 分析实线的几何特征
    print("\n分析实线几何特征:")
    
    # 提取实线的骨架
    from skimage.morphology import skeletonize
    skeleton = skeletonize(solid_line_mask.astype(bool))
    skeleton_coords = np.where(skeleton)
    
    if len(skeleton_coords[0]) > 0:
        skeleton_points = list(zip(skeleton_coords[1], skeleton_coords[0]))  # (x, y)
        print(f"  骨架点数: {len(skeleton_points)}")
        
        # 简单排序骨架点
        skeleton_points.sort(key=lambda p: p[0])  # 按x坐标排序
        
        # 分析实线宽度
        widths = []
        for i in range(0, len(skeleton_points), 50):  # 每50个点采样一次
            x, y = skeleton_points[i]
            
            # 在该点垂直方向上计算宽度
            for offset in range(1, 50):
                # 向上和向下搜索
                if (y - offset >= 0 and solid_line_mask[y - offset, x] == 0 and
                    y + offset < h and solid_line_mask[y + offset, x] == 0):
                    width = offset * 2
                    widths.append(width)
                    break
        
        if widths:
            avg_width = np.mean(widths)
            print(f"  平均宽度: {avg_width:.1f} 像素")
            print(f"  宽度范围: {np.min(widths):.1f} - {np.max(widths):.1f}")
        
        # 保存骨架可视化
        skeleton_vis = np.zeros((h, w, 3), dtype=np.uint8)
        skeleton_vis[solid_line_mask > 0] = [100, 100, 100]  # 灰色实线
        skeleton_vis[skeleton] = [255, 255, 255]  # 白色骨架
        cv2.imwrite(f"{output_dir}/solid_line_skeleton.jpg", skeleton_vis)
    
    # 7. 如果有邻接关系，分析具体的邻接区域
    if adjacency_pixels > 0:
        print("\n分析邻接区域:")
        
        # 找到邻接区域的连通组件
        num_labels, labels = cv2.connectedComponents(adjacency.astype(np.uint8))
        
        print(f"  邻接连通组件数: {num_labels - 1}")
        
        for label in range(1, min(6, num_labels)):  # 只分析前5个
            component_mask = (labels == label)
            component_pixels = np.sum(component_mask)
            
            if component_pixels < 100:  # 忽略太小的组件
                continue
            
            print(f"  组件 {label}: {component_pixels:,} 像素")
            
            # 找到组件的边界
            coords = np.where(component_mask)
            y_min, y_max = np.min(coords[0]), np.max(coords[0])
            x_min, x_max = np.min(coords[1]), np.max(coords[1])
            center_x = (x_min + x_max) // 2
            center_y = (y_min + y_max) // 2
            
            print(f"    中心: ({center_x}, {center_y})")
            print(f"    边界: x[{x_min}-{x_max}], y[{y_min}-{y_max}]")
            
            # 可视化该组件
            component_vis = np.zeros((h, w, 3), dtype=np.uint8)
            component_vis[solid_line_mask > 0] = [255, 255, 255]  # 白色实线
            component_vis[component_mask] = [0, 255, 0]  # 绿色邻接区域
            cv2.circle(component_vis, (center_x, center_y), 5, (255, 0, 0), -1)  # 红色中心点
            
            cv2.imwrite(f"{output_dir}/adjacency_component_{label}.jpg", component_vis)
    
    print(f"\n结果已保存到: {output_dir}")
    
    # 8. 总结
    print("\n=== 实线分析总结 ===")
    if overlap_pixels > 0:
        print("✅ 发现直接重叠：遮挡物直接覆盖了实线")
        print("   → 这种情况需要在重叠区域补全实线")
    elif adjacency_pixels > 0:
        print("✅ 发现邻接关系：遮挡物贴近实线但不覆盖")
        print("   → 这种情况需要判断是否真的需要补全")
        print("   → 可能遮挡物只是在实线旁边，不需要补全")
    else:
        print("❌ 没有发现重叠或邻接关系")
        print("   → 实线和遮挡物在空间上分离")
    
    return {
        'solid_line_pixels': np.sum(solid_line_mask),
        'occlusion_pixels': np.sum(unified_occlusion_mask),
        'overlap_pixels': overlap_pixels,
        'adjacency_pixels': adjacency_pixels,
        'has_skeleton': len(skeleton_coords[0]) > 0 if 'skeleton_coords' in locals() else False
    }

if __name__ == "__main__":
    try:
        result = test_solid_line_only()
        print("\n实线分析完成！")
        
    except Exception as e:
        print(f"实线分析出错: {e}")
        import traceback
        traceback.print_exc()
