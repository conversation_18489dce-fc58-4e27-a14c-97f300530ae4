"""
调试mask像素值分布
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import cv2

def debug_mask_values():
    """调试mask像素值"""
    print("=== 调试mask像素值分布 ===")
    
    # 加载mask
    mask_path = "data/masks/DJI_20220915153843_0320.png"
    mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
    
    if mask is None:
        print("无法加载mask文件")
        return
    
    # 缩放
    scale_factor = 0.25
    mask = cv2.resize(mask, None, fx=scale_factor, fy=scale_factor, interpolation=cv2.INTER_NEAREST)
    
    print(f"Mask尺寸: {mask.shape}")
    
    # 统计像素值分布
    unique_values, counts = np.unique(mask, return_counts=True)
    
    print("像素值分布:")
    for value, count in zip(unique_values, counts):
        percentage = count / mask.size * 100
        print(f"  像素值 {value}: {count:,} 像素 ({percentage:.2f}%)")
    
    # 检查特定的遮挡物像素值
    occlusion_pixels = [1, 2, 11, 12]
    print("\n遮挡物像素检查:")
    for pixel_value in occlusion_pixels:
        count = np.sum(mask == pixel_value)
        print(f"  像素值 {pixel_value}: {count:,} 像素")
    
    # 检查标线像素值
    lane_pixels = [3, 4, 5, 6, 7, 8, 9, 10]
    print("\n标线像素检查:")
    for pixel_value in lane_pixels:
        count = np.sum(mask == pixel_value)
        print(f"  像素值 {pixel_value}: {count:,} 像素")

if __name__ == "__main__":
    debug_mask_values()
