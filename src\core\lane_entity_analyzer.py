"""
标线实体分析器
基于用户专业理解的标线属性分析和实体识别
"""

import numpy as np
import cv2
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from skimage.morphology import skeletonize
import logging

from ..models.mask_model import LaneType


@dataclass
class LaneEdge:
    """标线边缘"""
    points: List[Tuple[int, int]]  # 边缘点序列
    direction: np.ndarray          # 主方向向量
    curvature: float              # 曲率
    width_at_points: List[float]  # 各点处的宽度


@dataclass
class LaneEntity:
    """标线实体"""
    lane_type: LaneType
    center_line: List[Tuple[int, int]]  # 中心线点序列
    left_edge: LaneEdge                 # 左边缘
    right_edge: LaneEdge                # 右边缘
    width_profile: List[float]          # 宽度分布
    direction_profile: List[np.ndarray] # 方向分布

    # 特殊属性（根据类型）
    dash_segments: Optional[List] = None      # 虚线段信息
    parallel_lines: Optional[List] = None     # 平行线信息（双线、斑马线等）
    fill_pattern: Optional[Dict] = None       # 填充模式（网格区、导流区）


@dataclass
class OcclusionBreak:
    """遮挡断裂"""
    entity_id: int
    break_start: Tuple[int, int]  # 断裂起点
    break_end: Tuple[int, int]    # 断裂终点
    occlusion_region: np.ndarray  # 遮挡区域

    # 断裂两端的几何特征
    start_edge_left: Tuple[int, int]
    start_edge_right: Tuple[int, int]
    start_direction: np.ndarray
    start_width: float
    start_curvature: float

    end_edge_left: Tuple[int, int]
    end_edge_right: Tuple[int, int]
    end_direction: np.ndarray
    end_width: float
    end_curvature: float


class LaneEntityAnalyzer:
    """标线实体分析器"""

    def __init__(self, config: Dict = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)

        # 配置参数
        self.edge_detection_kernel = self.config.get('edge_detection_kernel', 3)
        self.skeleton_smoothing = self.config.get('skeleton_smoothing', True)
        self.curvature_window = self.config.get('curvature_window', 5)
        self.width_sampling_interval = self.config.get('width_sampling_interval', 10)

        # 误差容忍参数
        self.direction_tolerance = self.config.get('direction_tolerance', 15)  # 度
        self.width_tolerance = self.config.get('width_tolerance', 0.3)  # 30%
        self.parallel_tolerance = self.config.get('parallel_tolerance', 10)  # 度

    def analyze_lane_entities(self,
                            lane_masks: Dict[str, np.ndarray],
                            lane_types: Dict[str, LaneType],
                            occlusion_mask: np.ndarray) -> Tuple[List[LaneEntity], List[OcclusionBreak]]:
        """
        分析标线实体和遮挡断裂

        Args:
            lane_masks: 标线mask字典
            lane_types: 标线类型字典
            occlusion_mask: 统一的遮挡物mask

        Returns:
            (标线实体列表, 遮挡断裂列表)
        """
        print("开始分析标线实体...")

        # 1. 为每种标线类型创建实体
        lane_entities = []
        for lane_name, lane_mask in lane_masks.items():
            lane_type = lane_types[lane_name]

            print(f"  分析标线: {lane_name} ({lane_type.value})")

            # 分析标线的基本几何属性
            entity = self._analyze_single_lane_entity(lane_mask, lane_type)
            if entity:
                lane_entities.append(entity)
                print(f"    中心线长度: {len(entity.center_line)}")
                print(f"    平均宽度: {np.mean(entity.width_profile):.1f}")

        # 2. 检测遮挡断裂
        print("检测遮挡断裂...")
        occlusion_breaks = []

        for i, entity in enumerate(lane_entities):
            breaks = self._detect_occlusion_breaks(entity, occlusion_mask, i)
            occlusion_breaks.extend(breaks)

            if breaks:
                print(f"  实体 {i} ({entity.lane_type.value}): 检测到 {len(breaks)} 个断裂")

        return lane_entities, occlusion_breaks

    def _analyze_single_lane_entity(self, lane_mask: np.ndarray, lane_type: LaneType) -> Optional[LaneEntity]:
        """分析单个标线实体"""
        if not np.any(lane_mask):
            return None

        # 1. 提取中心线（骨架）
        center_line = self._extract_center_line(lane_mask)
        if len(center_line) < 10:  # 太短的线条忽略
            return None

        # 2. 提取边缘线
        left_edge, right_edge = self._extract_edge_lines(lane_mask, center_line)

        # 3. 计算宽度分布
        width_profile = self._calculate_width_profile(center_line, left_edge, right_edge)

        # 4. 计算方向分布
        direction_profile = self._calculate_direction_profile(center_line)

        # 5. 根据标线类型分析特殊属性
        special_attrs = self._analyze_special_attributes(lane_mask, lane_type)

        return LaneEntity(
            lane_type=lane_type,
            center_line=center_line,
            left_edge=left_edge,
            right_edge=right_edge,
            width_profile=width_profile,
            direction_profile=direction_profile,
            **special_attrs
        )

    def _extract_center_line(self, lane_mask: np.ndarray) -> List[Tuple[int, int]]:
        """提取标线的中心线"""
        # 使用骨架化提取中心线
        skeleton = skeletonize(lane_mask.astype(bool))

        # 获取骨架点
        skeleton_coords = np.where(skeleton)
        if len(skeleton_coords[0]) == 0:
            return []

        # 将骨架点排序成连续的线
        points = list(zip(skeleton_coords[1], skeleton_coords[0]))  # (x, y)

        # 简化的排序：按x坐标排序（对于大多数情况有效）
        points.sort(key=lambda p: p[0])

        return points

    def _extract_edge_lines(self, lane_mask: np.ndarray, center_line: List[Tuple[int, int]]) -> Tuple[LaneEdge, LaneEdge]:
        """提取标线的边缘线"""
        # 使用形态学操作检测边缘
        kernel = np.ones((self.edge_detection_kernel, self.edge_detection_kernel), np.uint8)

        # 膨胀和腐蚀来找边缘
        dilated = cv2.dilate(lane_mask.astype(np.uint8), kernel, iterations=1)
        eroded = cv2.erode(lane_mask.astype(np.uint8), kernel, iterations=1)
        edges = dilated - eroded

        # 找到边缘轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if len(contours) == 0:
            # 如果没有找到边缘，使用简化方法
            return self._create_simple_edges(center_line)

        # 选择最大的轮廓作为边缘
        main_contour = max(contours, key=cv2.contourArea)
        edge_points = [(point[0][0], point[0][1]) for point in main_contour]

        # 将边缘点分为左右两边
        left_points, right_points = self._split_edge_points(edge_points, center_line)

        # 计算边缘属性
        left_edge = LaneEdge(
            points=left_points,
            direction=self._calculate_edge_direction(left_points),
            curvature=self._calculate_edge_curvature(left_points),
            width_at_points=[]
        )

        right_edge = LaneEdge(
            points=right_points,
            direction=self._calculate_edge_direction(right_points),
            curvature=self._calculate_edge_curvature(right_points),
            width_at_points=[]
        )

        return left_edge, right_edge

    def _create_simple_edges(self, center_line: List[Tuple[int, int]]) -> Tuple[LaneEdge, LaneEdge]:
        """创建简化的边缘线"""
        # 简化版本：假设边缘平行于中心线
        left_points = [(x-2, y) for x, y in center_line]
        right_points = [(x+2, y) for x, y in center_line]

        left_edge = LaneEdge(
            points=left_points,
            direction=np.array([1.0, 0.0]),
            curvature=0.0,
            width_at_points=[]
        )

        right_edge = LaneEdge(
            points=right_points,
            direction=np.array([1.0, 0.0]),
            curvature=0.0,
            width_at_points=[]
        )

        return left_edge, right_edge

    def _split_edge_points(self, edge_points: List[Tuple[int, int]],
                          center_line: List[Tuple[int, int]]) -> Tuple[List[Tuple[int, int]], List[Tuple[int, int]]]:
        """将边缘点分为左右两边"""
        if len(center_line) < 2:
            mid = len(edge_points) // 2
            return edge_points[:mid], edge_points[mid:]

        # 计算中心线的主方向
        center_start = np.array(center_line[0])
        center_end = np.array(center_line[-1])
        main_direction = center_end - center_start
        main_direction = main_direction / np.linalg.norm(main_direction)

        # 计算垂直方向
        perpendicular = np.array([-main_direction[1], main_direction[0]])

        # 根据垂直方向分离左右边缘
        left_points = []
        right_points = []

        center_point = np.array(center_line[len(center_line)//2])

        for point in edge_points:
            point_vec = np.array(point) - center_point
            if np.dot(point_vec, perpendicular) > 0:
                left_points.append(point)
            else:
                right_points.append(point)

        return left_points, right_points

    def _calculate_edge_direction(self, edge_points: List[Tuple[int, int]]) -> np.ndarray:
        """计算边缘的主方向"""
        if len(edge_points) < 2:
            return np.array([1.0, 0.0])

        points = np.array(edge_points)

        # 使用PCA计算主方向
        mean_point = np.mean(points, axis=0)
        centered_points = points - mean_point

        cov_matrix = np.cov(centered_points.T)
        eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)

        main_direction = eigenvectors[:, np.argmax(eigenvalues)]
        return main_direction

    def _calculate_edge_curvature(self, edge_points: List[Tuple[int, int]]) -> float:
        """计算边缘的曲率"""
        if len(edge_points) < 3:
            return 0.0

        # 简化的曲率计算
        curvatures = []
        for i in range(1, len(edge_points) - 1):
            p1 = np.array(edge_points[i-1])
            p2 = np.array(edge_points[i])
            p3 = np.array(edge_points[i+1])

            v1 = p2 - p1
            v2 = p3 - p2

            len1 = np.linalg.norm(v1)
            len2 = np.linalg.norm(v2)

            if len1 > 0 and len2 > 0:
                cos_angle = np.dot(v1, v2) / (len1 * len2)
                cos_angle = np.clip(cos_angle, -1, 1)
                angle = np.arccos(cos_angle)
                curvatures.append(angle)

        return np.mean(curvatures) if curvatures else 0.0

    def _calculate_width_profile(self, center_line: List[Tuple[int, int]],
                               left_edge: LaneEdge, right_edge: LaneEdge) -> List[float]:
        """计算宽度分布"""
        widths = []

        for i in range(0, len(center_line), self.width_sampling_interval):
            center_point = np.array(center_line[i])

            # 找到最近的左右边缘点
            if left_edge.points and right_edge.points:
                left_distances = [np.linalg.norm(np.array(p) - center_point) for p in left_edge.points]
                right_distances = [np.linalg.norm(np.array(p) - center_point) for p in right_edge.points]

                min_left_dist = min(left_distances) if left_distances else 0
                min_right_dist = min(right_distances) if right_distances else 0

                width = min_left_dist + min_right_dist
                widths.append(width)
            else:
                widths.append(5.0)  # 默认宽度

        return widths if widths else [5.0]

    def _calculate_direction_profile(self, center_line: List[Tuple[int, int]]) -> List[np.ndarray]:
        """计算方向分布"""
        directions = []

        for i in range(len(center_line) - 1):
            p1 = np.array(center_line[i])
            p2 = np.array(center_line[i + 1])

            direction = p2 - p1
            if np.linalg.norm(direction) > 0:
                direction = direction / np.linalg.norm(direction)
            else:
                direction = np.array([1.0, 0.0])

            directions.append(direction)

        # 最后一个点使用前一个方向
        if directions:
            directions.append(directions[-1])
        else:
            directions.append(np.array([1.0, 0.0]))

        return directions

    def _analyze_special_attributes(self, lane_mask: np.ndarray, lane_type: LaneType) -> Dict:
        """分析特殊属性"""
        special_attrs = {}

        if lane_type == LaneType.DASHED_LINE:
            # 分析虚线段
            special_attrs['dash_segments'] = self._analyze_dash_segments(lane_mask)
        elif lane_type == LaneType.DOUBLE_LINE:
            # 分析双线
            special_attrs['parallel_lines'] = self._analyze_parallel_lines(lane_mask)
        elif lane_type == LaneType.ZEBRA:
            # 分析斑马线
            special_attrs['parallel_lines'] = self._analyze_zebra_lines(lane_mask)
        elif lane_type in [LaneType.GRID_AREA, LaneType.DIVERGE_AREA]:
            # 分析填充模式
            special_attrs['fill_pattern'] = self._analyze_fill_pattern(lane_mask, lane_type)

        return special_attrs

    def _analyze_dash_segments(self, lane_mask: np.ndarray) -> List[Dict]:
        """分析虚线段"""
        # 获取连通组件
        num_labels, labels = cv2.connectedComponents(lane_mask.astype(np.uint8))

        segments = []
        for label in range(1, num_labels):
            segment_mask = (labels == label)
            if np.sum(segment_mask) < 5:
                continue

            # 分析段的属性
            coords = np.where(segment_mask)
            center = (np.mean(coords[1]), np.mean(coords[0]))

            contours, _ = cv2.findContours(segment_mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            if len(contours) > 0:
                rect = cv2.minAreaRect(contours[0])
                length = max(rect[1])
                width = min(rect[1])
                angle = rect[2]

                segments.append({
                    'center': center,
                    'length': length,
                    'width': width,
                    'angle': angle,
                    'mask': segment_mask
                })

        return segments

    def _analyze_parallel_lines(self, lane_mask: np.ndarray) -> List[Dict]:
        """分析平行线（双线）"""
        # 简化实现
        return []

    def _analyze_zebra_lines(self, lane_mask: np.ndarray) -> List[Dict]:
        """分析斑马线"""
        # 简化实现
        return []

    def _analyze_fill_pattern(self, lane_mask: np.ndarray, lane_type: LaneType) -> Dict:
        """分析填充模式"""
        # 简化实现
        return {'pattern_type': 'grid' if lane_type == LaneType.GRID_AREA else 'diverge'}

    def _detect_occlusion_breaks(self, entity: LaneEntity, occlusion_mask: np.ndarray, entity_id: int) -> List[OcclusionBreak]:
        """
        检测遮挡断裂
        基于邻接关系而非重叠关系
        """
        breaks = []

        # 1. 检查标线与遮挡物的邻接关系
        # 创建标线的膨胀版本来检测邻接
        lane_mask = np.zeros(occlusion_mask.shape, dtype=np.uint8)
        for point in entity.center_line:
            x, y = point
            if 0 <= x < lane_mask.shape[1] and 0 <= y < lane_mask.shape[0]:
                lane_mask[y, x] = 1

        # 膨胀标线以检测邻接
        kernel = np.ones((15, 15), np.uint8)  # 15像素邻接范围
        dilated_lane = cv2.dilate(lane_mask, kernel, iterations=1)

        # 找到邻接区域
        adjacency_region = np.logical_and(dilated_lane, occlusion_mask)

        if not np.any(adjacency_region):
            return breaks  # 没有邻接关系

        # 2. 基于邻接区域推断可能的断裂
        # 找到邻接区域的连通组件
        num_labels, labels = cv2.connectedComponents(adjacency_region.astype(np.uint8))

        for label in range(1, num_labels):
            component_mask = (labels == label)
            if np.sum(component_mask) < 50:  # 忽略太小的邻接区域
                continue

            # 找到该邻接区域对应的标线段
            break_obj = self._create_adjacency_based_break(
                entity, entity_id, component_mask, occlusion_mask
            )
            if break_obj:
                breaks.append(break_obj)

        return breaks

    def _create_adjacency_based_break(self, entity: LaneEntity, entity_id: int,
                                    adjacency_component: np.ndarray, occlusion_mask: np.ndarray) -> Optional[OcclusionBreak]:
        """
        基于邻接关系创建断裂对象
        推断邻接区域附近应该有标线但被遮挡了
        """
        # 1. 找到邻接区域的中心和边界
        adj_coords = np.where(adjacency_component)
        if len(adj_coords[0]) == 0:
            return None

        adj_center_y = int(np.mean(adj_coords[0]))
        adj_center_x = int(np.mean(adj_coords[1]))

        # 2. 找到最接近邻接区域的标线点
        center_points = entity.center_line
        min_distance = float('inf')
        closest_point_idx = 0

        for i, point in enumerate(center_points):
            x, y = point
            distance = np.sqrt((x - adj_center_x)**2 + (y - adj_center_y)**2)
            if distance < min_distance:
                min_distance = distance
                closest_point_idx = i

        # 3. 基于最近点推断断裂的起点和终点
        # 这里简化：假设邻接区域就是需要补全的区域
        break_start = (adj_center_x, adj_center_y)
        break_end = (adj_center_x, adj_center_y)  # 简化为同一点

        # 4. 分析断裂点的几何特征
        start_geom = self._analyze_break_point_geometry(entity, closest_point_idx)
        end_geom = start_geom.copy()  # 简化：使用相同的几何特征

        # 5. 创建遮挡区域（邻接区域周围的区域）
        y_min, y_max = np.min(adj_coords[0]), np.max(adj_coords[0])
        x_min, x_max = np.min(adj_coords[1]), np.max(adj_coords[1])

        # 扩展边界
        margin = 20
        y_min = max(0, y_min - margin)
        y_max = min(occlusion_mask.shape[0], y_max + margin)
        x_min = max(0, x_min - margin)
        x_max = min(occlusion_mask.shape[1], x_max + margin)

        occlusion_region = occlusion_mask[y_min:y_max, x_min:x_max]

        return OcclusionBreak(
            entity_id=entity_id,
            break_start=break_start,
            break_end=break_end,
            occlusion_region=occlusion_region,
            start_edge_left=start_geom['edge_left'],
            start_edge_right=start_geom['edge_right'],
            start_direction=start_geom['direction'],
            start_width=start_geom['width'],
            start_curvature=start_geom['curvature'],
            end_edge_left=end_geom['edge_left'],
            end_edge_right=end_geom['edge_right'],
            end_direction=end_geom['direction'],
            end_width=end_geom['width'],
            end_curvature=end_geom['curvature']
        )

    def _create_occlusion_break(self, entity: LaneEntity, entity_id: int,
                              start_idx: int, end_idx: int, occlusion_mask: np.ndarray) -> Optional[OcclusionBreak]:
        """创建遮挡断裂对象"""
        center_points = entity.center_line

        if start_idx <= 0 or end_idx >= len(center_points) - 1:
            return None

        # 断裂的起点和终点
        break_start = center_points[start_idx]
        break_end = center_points[end_idx]

        # 提取遮挡区域
        y_coords = [p[1] for p in center_points[start_idx:end_idx+1]]
        x_coords = [p[0] for p in center_points[start_idx:end_idx+1]]

        y_min, y_max = min(y_coords), max(y_coords)
        x_min, x_max = min(x_coords), max(x_coords)

        # 扩展一点边界
        margin = 10
        y_min = max(0, y_min - margin)
        y_max = min(occlusion_mask.shape[0], y_max + margin)
        x_min = max(0, x_min - margin)
        x_max = min(occlusion_mask.shape[1], x_max + margin)

        occlusion_region = occlusion_mask[y_min:y_max, x_min:x_max]

        # 分析断裂两端的几何特征
        start_geom = self._analyze_break_point_geometry(entity, start_idx - 1)
        end_geom = self._analyze_break_point_geometry(entity, end_idx + 1)

        return OcclusionBreak(
            entity_id=entity_id,
            break_start=break_start,
            break_end=break_end,
            occlusion_region=occlusion_region,
            **start_geom,
            **{f"end_{k}": v for k, v in end_geom.items()}
        )

    def _analyze_break_point_geometry(self, entity: LaneEntity, point_idx: int) -> Dict:
        """分析断裂点的几何特征"""
        if point_idx < 0 or point_idx >= len(entity.center_line):
            return {
                'edge_left': (0, 0),
                'edge_right': (0, 0),
                'direction': np.array([1.0, 0.0]),
                'width': 5.0,
                'curvature': 0.0
            }

        center_point = entity.center_line[point_idx]
        direction = entity.direction_profile[point_idx] if point_idx < len(entity.direction_profile) else np.array([1.0, 0.0])
        width = entity.width_profile[min(point_idx // self.width_sampling_interval, len(entity.width_profile) - 1)]

        # 计算边缘点（简化）
        perpendicular = np.array([-direction[1], direction[0]])
        edge_left = (
            int(center_point[0] + perpendicular[0] * width / 2),
            int(center_point[1] + perpendicular[1] * width / 2)
        )
        edge_right = (
            int(center_point[0] - perpendicular[0] * width / 2),
            int(center_point[1] - perpendicular[1] * width / 2)
        )

        return {
            'edge_left': edge_left,
            'edge_right': edge_right,
            'direction': direction,
            'width': width,
            'curvature': 0.0  # 简化
        }
