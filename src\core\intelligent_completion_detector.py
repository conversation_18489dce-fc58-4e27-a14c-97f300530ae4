"""
智能补全检测器
基于用户专业理解的触发和补全逻辑
"""

import numpy as np
import cv2
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass
import logging

from ..models.mask_model import (
    LaneMask, LaneSegment, OcclusionRegion, OcclusionType, LaneType,
    OCCLUSION_PIXELS, OCCLUSION_TYPE_MAP
)


@dataclass
class CompletionTrigger:
    """补全触发条件"""
    occlusion_id: int
    surrounding_lanes: List[int]  # 周围的同类标线
    lane_type: LaneType
    trigger_confidence: float
    occlusion_area: np.ndarray  # 遮挡区域
    missing_area: np.ndarray    # 推断的缺失标线区域


@dataclass
class LaneHierarchy:
    """标线层次关系"""
    boundary_lanes: List[int]    # 边界标线（实线等）
    internal_lanes: List[int]    # 内部标线（虚线等）
    area_lanes: List[int]        # 区域标线（网格区、导流区等）


class IntelligentCompletionDetector:
    """智能补全检测器"""

    def __init__(self, config: Dict = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)

        # 配置参数
        self.search_radius = self.config.get('search_radius', 50)  # 搜索半径
        self.same_type_threshold = self.config.get('same_type_threshold', 0.3)  # 同类型阈值
        self.trigger_confidence_threshold = self.config.get('trigger_confidence_threshold', 0.5)

        # 标线层次关系定义
        self.boundary_types = {LaneType.SOLID_LINE, LaneType.DOUBLE_LINE, LaneType.STOP_LINE}
        self.internal_types = {LaneType.DASHED_LINE}
        self.area_types = {LaneType.GRID_AREA, LaneType.DIVERGE_AREA}
        self.arrow_types = {LaneType.ARROW_STRAIGHT, LaneType.ARROW_LEFT, LaneType.ARROW_RIGHT,
                           LaneType.LEFT_RIGHT, LaneType.ARROW_STRAIGHT_LEFT, LaneType.ARROW_STRAIGHT_RIGHT}

    def detect_occlusions_and_triggers(self, grayscale_mask: np.ndarray) -> Tuple[Dict, Dict, List[CompletionTrigger]]:
        """
        检测遮挡物并分析补全触发条件

        Args:
            grayscale_mask: 灰度mask图像

        Returns:
            (occlusion_masks, occlusion_types, completion_triggers)
        """
        # 1. 检测遮挡物
        occlusion_masks, occlusion_types = self._detect_occlusions(grayscale_mask)

        # 2. 解析标线
        lane_masks, lane_types = self._parse_lanes(grayscale_mask)

        # 3. 分析每个遮挡物周围的标线情况
        completion_triggers = []

        for occlusion_name, occlusion_mask in occlusion_masks.items():
            print(f"\n分析遮挡物: {occlusion_name}")

            # 分析周围的标线
            surrounding_analysis = self._analyze_surrounding_lanes(
                occlusion_mask, lane_masks, lane_types
            )

            # 检查是否触发补全
            triggers = self._check_completion_triggers(
                occlusion_mask, surrounding_analysis, occlusion_name
            )

            completion_triggers.extend(triggers)

        return occlusion_masks, occlusion_types, completion_triggers

    def _detect_occlusions(self, grayscale_mask: np.ndarray) -> Tuple[Dict, Dict]:
        """检测遮挡物"""
        occlusion_masks = {}
        occlusion_types = {}

        for pixel_value, occlusion_name in OCCLUSION_TYPE_MAP.items():
            occlusion_mask = (grayscale_mask == pixel_value).astype(np.uint8)

            if np.sum(occlusion_mask) > 0:
                if occlusion_name in ['car', 'human']:
                    occ_type = OcclusionType.DYNAMIC
                else:
                    occ_type = OcclusionType.STATIC

                occlusion_masks[occlusion_name] = occlusion_mask
                occlusion_types[occlusion_name] = occ_type

                print(f"检测到遮挡物 {occlusion_name}: {np.sum(occlusion_mask)} 像素")

        return occlusion_masks, occlusion_types

    def _parse_lanes(self, grayscale_mask: np.ndarray) -> Tuple[Dict, Dict]:
        """解析标线"""
        from ..utils.mask_processor import MaskProcessor
        processor = MaskProcessor()
        lane_masks, lane_types, _, _ = processor.parse_grayscale_mask(grayscale_mask)
        return lane_masks, lane_types

    def _analyze_surrounding_lanes(self,
                                 occlusion_mask: np.ndarray,
                                 lane_masks: Dict[str, np.ndarray],
                                 lane_types: Dict[str, LaneType]) -> Dict:
        """
        分析遮挡物周围的标线情况

        Args:
            occlusion_mask: 遮挡物mask
            lane_masks: 标线mask字典
            lane_types: 标线类型字典

        Returns:
            周围标线分析结果
        """
        # 1. 创建搜索区域（遮挡物周围的扩展区域）
        kernel = np.ones((self.search_radius, self.search_radius), np.uint8)
        search_area = cv2.dilate(occlusion_mask, kernel, iterations=1)

        # 2. 分析每种标线类型在搜索区域内的情况
        surrounding_lanes = {}

        for lane_name, lane_mask in lane_masks.items():
            lane_type = lane_types[lane_name]

            # 计算标线在搜索区域内的像素
            lane_in_search = np.logical_and(lane_mask, search_area)
            lane_pixels_in_search = np.sum(lane_in_search)

            # 计算标线与遮挡物的距离
            if lane_pixels_in_search > 0:
                min_distance = self._calculate_min_distance(lane_mask, occlusion_mask)

                surrounding_lanes[lane_name] = {
                    'lane_type': lane_type,
                    'pixels_in_search': lane_pixels_in_search,
                    'total_pixels': np.sum(lane_mask),
                    'min_distance': min_distance,
                    'mask': lane_mask
                }

                print(f"  周围标线 {lane_name} ({lane_type.value}): "
                      f"{lane_pixels_in_search} 像素, 距离 {min_distance:.1f}")

        return surrounding_lanes

    def _calculate_min_distance(self, lane_mask: np.ndarray, occlusion_mask: np.ndarray) -> float:
        """计算标线与遮挡物的最小距离（优化版本）"""
        # 使用距离变换来快速计算最小距离

        # 创建标线的距离变换
        lane_dist_transform = cv2.distanceTransform(
            (1 - lane_mask).astype(np.uint8),
            cv2.DIST_L2,
            cv2.DIST_MASK_PRECISE
        )

        # 在遮挡物区域内找到最小距离
        occlusion_coords = np.where(occlusion_mask)
        if len(occlusion_coords[0]) == 0:
            return float('inf')

        # 采样遮挡物点以提高效率
        sample_indices = np.arange(0, len(occlusion_coords[0]), max(1, len(occlusion_coords[0]) // 100))
        sampled_distances = lane_dist_transform[occlusion_coords[0][sample_indices],
                                              occlusion_coords[1][sample_indices]]

        return float(np.min(sampled_distances))

    def _check_completion_triggers(self,
                                 occlusion_mask: np.ndarray,
                                 surrounding_analysis: Dict,
                                 occlusion_name: str) -> List[CompletionTrigger]:
        """
        检查补全触发条件

        核心逻辑：如果障碍物周围有同一类别标线，触发修复
        """
        triggers = []

        # 按标线类型分组
        lanes_by_type = {}
        for lane_name, analysis in surrounding_analysis.items():
            lane_type = analysis['lane_type']
            if lane_type not in lanes_by_type:
                lanes_by_type[lane_type] = []
            lanes_by_type[lane_type].append((lane_name, analysis))

        # 检查每种标线类型
        for lane_type, lane_list in lanes_by_type.items():
            if len(lane_list) >= 1:  # 至少有1个同类型标线
                print(f"  触发条件检查: {lane_type.value} 有 {len(lane_list)} 个实例")

                # 计算触发置信度
                confidence = self._calculate_trigger_confidence(lane_list, occlusion_mask)

                if confidence >= self.trigger_confidence_threshold:
                    # 推断缺失的标线区域
                    missing_area = self._infer_missing_lane_area(
                        occlusion_mask, lane_list, lane_type
                    )

                    if np.sum(missing_area) > 0:
                        trigger = CompletionTrigger(
                            occlusion_id=hash(occlusion_name),  # 简化的ID
                            surrounding_lanes=[hash(name) for name, _ in lane_list],
                            lane_type=lane_type,
                            trigger_confidence=confidence,
                            occlusion_area=occlusion_mask,
                            missing_area=missing_area
                        )

                        triggers.append(trigger)
                        print(f"  ✅ 触发补全: {lane_type.value}, 置信度 {confidence:.3f}, "
                              f"缺失区域 {np.sum(missing_area)} 像素")

        return triggers

    def _calculate_trigger_confidence(self, lane_list: List, occlusion_mask: np.ndarray) -> float:
        """计算触发置信度"""
        # 基于周围标线的数量、距离、像素密度等计算置信度

        total_pixels = sum(analysis['pixels_in_search'] for _, analysis in lane_list)
        min_distance = min(analysis['min_distance'] for _, analysis in lane_list)
        num_lanes = len(lane_list)

        # 距离置信度（距离越近置信度越高）
        distance_confidence = max(0, 1.0 - min_distance / self.search_radius)

        # 数量置信度
        quantity_confidence = min(1.0, num_lanes / 3.0)  # 3个或以上标线 = 100%置信度

        # 像素密度置信度
        density_confidence = min(1.0, total_pixels / 1000.0)  # 1000像素 = 100%置信度

        # 综合置信度
        overall_confidence = (distance_confidence * 0.4 +
                            quantity_confidence * 0.3 +
                            density_confidence * 0.3)

        return overall_confidence

    def _infer_missing_lane_area(self,
                                occlusion_mask: np.ndarray,
                                lane_list: List,
                                lane_type: LaneType) -> np.ndarray:
        """
        推断缺失的标线区域

        核心思想：基于周围同类型标线的几何特征，推断遮挡物下方应该存在的标线
        """
        missing_area = np.zeros_like(occlusion_mask)

        # 收集周围标线的几何特征
        all_lane_pixels = []
        for lane_name, analysis in lane_list:
            lane_coords = np.where(analysis['mask'])
            if len(lane_coords[0]) > 0:
                all_lane_pixels.extend(list(zip(lane_coords[1], lane_coords[0])))  # (x, y)

        if len(all_lane_pixels) < 10:
            return missing_area

        # 根据标线类型推断缺失区域
        if lane_type in self.boundary_types:
            # 边界线：实线、双实线、停止线
            missing_area = self._infer_boundary_line_area(occlusion_mask, all_lane_pixels)
        elif lane_type in self.internal_types:
            # 内部线：虚线
            missing_area = self._infer_internal_line_area(occlusion_mask, all_lane_pixels)
        elif lane_type in self.area_types:
            # 区域：网格区、导流区
            missing_area = self._infer_area_region(occlusion_mask, all_lane_pixels)
        elif lane_type in self.arrow_types:
            # 箭头
            missing_area = self._infer_arrow_area(occlusion_mask, all_lane_pixels)

        return missing_area

    def _infer_boundary_line_area(self, occlusion_mask: np.ndarray, lane_pixels: List) -> np.ndarray:
        """推断边界线的缺失区域"""
        missing_area = np.zeros_like(occlusion_mask)

        # 使用线性回归拟合标线方向
        if len(lane_pixels) < 10:
            return missing_area

        points = np.array(lane_pixels)

        # 计算主方向
        mean_point = np.mean(points, axis=0)
        centered_points = points - mean_point

        # PCA计算主方向
        cov_matrix = np.cov(centered_points.T)
        eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)
        main_direction = eigenvectors[:, np.argmax(eigenvalues)]

        # 在遮挡区域内沿主方向绘制线段
        occlusion_coords = np.where(occlusion_mask)
        if len(occlusion_coords[0]) == 0:
            return missing_area

        # 计算遮挡区域的中心
        occlusion_center = np.array([
            np.mean(occlusion_coords[1]),  # x
            np.mean(occlusion_coords[0])   # y
        ])

        # 估计线宽（基于周围标线的密度）
        line_width = max(3, int(np.sqrt(len(lane_pixels)) / 10))

        # 沿主方向在遮挡区域内绘制线段
        length = max(np.max(occlusion_coords[1]) - np.min(occlusion_coords[1]),
                    np.max(occlusion_coords[0]) - np.min(occlusion_coords[0]))

        start_point = occlusion_center - main_direction * length / 2
        end_point = occlusion_center + main_direction * length / 2

        # 绘制线段
        cv2.line(missing_area,
                tuple(start_point.astype(int)),
                tuple(end_point.astype(int)),
                1, line_width)

        # 只保留在遮挡区域内的部分
        missing_area = np.logical_and(missing_area, occlusion_mask).astype(np.uint8)

        return missing_area

    def _infer_internal_line_area(self, occlusion_mask: np.ndarray, lane_pixels: List) -> np.ndarray:
        """推断内部线（虚线）的缺失区域"""
        # 虚线的推断类似边界线，但需要考虑虚线模式
        return self._infer_boundary_line_area(occlusion_mask, lane_pixels)

    def _infer_area_region(self, occlusion_mask: np.ndarray, lane_pixels: List) -> np.ndarray:
        """推断区域（网格区、导流区）的缺失区域"""
        missing_area = np.zeros_like(occlusion_mask)

        # 对于区域类型，假设整个遮挡区域都可能有标线
        # 但需要根据周围的模式来推断

        # 简化版本：在遮挡区域内创建网格模式
        occlusion_coords = np.where(occlusion_mask)
        if len(occlusion_coords[0]) == 0:
            return missing_area

        y_min, y_max = np.min(occlusion_coords[0]), np.max(occlusion_coords[0])
        x_min, x_max = np.min(occlusion_coords[1]), np.max(occlusion_coords[1])

        # 创建简单的网格模式
        grid_spacing = 20
        line_width = 2

        # 水平线
        for y in range(y_min, y_max, grid_spacing):
            cv2.line(missing_area, (x_min, y), (x_max, y), 1, line_width)

        # 垂直线
        for x in range(x_min, x_max, grid_spacing):
            cv2.line(missing_area, (x, y_min), (x, y_max), 1, line_width)

        # 只保留在遮挡区域内的部分
        missing_area = np.logical_and(missing_area, occlusion_mask).astype(np.uint8)

        return missing_area

    def _infer_arrow_area(self, occlusion_mask: np.ndarray, lane_pixels: List) -> np.ndarray:
        """推断箭头的缺失区域"""
        missing_area = np.zeros_like(occlusion_mask)

        # 箭头的推断比较复杂，这里简化为矩形区域
        occlusion_coords = np.where(occlusion_mask)
        if len(occlusion_coords[0]) == 0:
            return missing_area

        y_min, y_max = np.min(occlusion_coords[0]), np.max(occlusion_coords[0])
        x_min, x_max = np.min(occlusion_coords[1]), np.max(occlusion_coords[1])

        # 在遮挡区域内创建矩形
        cv2.rectangle(missing_area, (x_min, y_min), (x_max, y_max), 1, -1)

        # 只保留在遮挡区域内的部分
        missing_area = np.logical_and(missing_area, occlusion_mask).astype(np.uint8)

        return missing_area
