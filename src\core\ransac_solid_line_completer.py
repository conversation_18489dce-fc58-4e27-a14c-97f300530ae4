"""
基于RANSAC算法的实线补全器
专门针对实线的高精度补全，使用RANSAC算法拟合线性模型
"""

import numpy as np
import cv2
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import random
from scipy import ndimage
from skimage.morphology import skeletonize

from ..models.mask_model import LaneSegment, OcclusionRegion, LaneType


@dataclass
class RANSACLineModel:
    """RANSAC线性模型"""
    point: np.ndarray  # 线上一点
    direction: np.ndarray  # 方向向量（单位向量）
    width: float  # 线宽
    confidence: float  # 模型置信度
    inliers: List[Tuple[int, int]]  # 内点列表
    

@dataclass
class SolidLineGeometry:
    """实线几何特征"""
    center_points: List[Tuple[int, int]]  # 中心线点
    edge_points_left: List[Tuple[int, int]]  # 左边缘点
    edge_points_right: List[Tuple[int, int]]  # 右边缘点
    average_width: float  # 平均宽度
    main_direction: np.ndarray  # 主方向
    curvature: float  # 曲率


class RANSACSolidLineCompleter:
    """基于RANSAC的实线补全器"""
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化RANSAC实线补全器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # RANSAC参数
        self.max_iterations = self.config.get('max_iterations', 1000)
        self.distance_threshold = self.config.get('distance_threshold', 3.0)
        self.min_inliers_ratio = self.config.get('min_inliers_ratio', 0.6)
        self.min_inliers_count = self.config.get('min_inliers_count', 10)
        
        # 几何参数
        self.width_tolerance = self.config.get('width_tolerance', 0.3)
        self.direction_tolerance = self.config.get('direction_tolerance', 0.2)
        self.max_gap_distance = self.config.get('max_gap_distance', 50)
        
        # 补全参数
        self.completion_step = self.config.get('completion_step', 1.0)
        self.edge_smoothing = self.config.get('edge_smoothing', True)
        
    def complete_solid_line(self,
                           lane_segment: LaneSegment,
                           occlusion_region: OcclusionRegion,
                           context: Dict[str, Any]) -> np.ndarray:
        """
        使用RANSAC算法补全实线
        
        Args:
            lane_segment: 实线段
            occlusion_region: 遮挡区域
            context: 上下文信息
            
        Returns:
            补全的mask
        """
        print("🔧 开始RANSAC实线补全...")
        
        # 1. 分析实线几何特征
        geometry = self._analyze_solid_line_geometry(lane_segment.mask)
        if geometry is None:
            print("❌ 无法分析实线几何特征")
            return np.zeros_like(occlusion_region.mask)
        
        print(f"📏 实线特征: 宽度={geometry.average_width:.1f}, "
              f"中心点数={len(geometry.center_points)}, "
              f"曲率={geometry.curvature:.3f}")
        
        # 2. 检测遮挡断裂点
        break_points = self._detect_occlusion_breaks(
            geometry, occlusion_region.mask
        )
        
        if len(break_points) < 2:
            print("❌ 未检测到足够的断裂点")
            return np.zeros_like(occlusion_region.mask)
        
        print(f"🔍 检测到 {len(break_points)} 个断裂点")
        
        # 3. 使用RANSAC拟合线性模型
        line_model = self._fit_line_with_ransac(geometry, break_points)
        if line_model is None:
            print("❌ RANSAC拟合失败")
            return np.zeros_like(occlusion_region.mask)
        
        print(f"✅ RANSAC拟合成功: 置信度={line_model.confidence:.3f}, "
              f"内点数={len(line_model.inliers)}")
        
        # 4. 生成补全mask
        completion_mask = self._generate_completion_mask(
            line_model, break_points, occlusion_region.mask, geometry
        )
        
        completed_pixels = np.sum(completion_mask)
        print(f"🎯 补全完成: {completed_pixels} 像素")
        
        return completion_mask
        
    def _analyze_solid_line_geometry(self, lane_mask: np.ndarray) -> Optional[SolidLineGeometry]:
        """分析实线的几何特征"""
        if not np.any(lane_mask):
            return None
        
        # 1. 提取中心线（骨架）
        skeleton = skeletonize(lane_mask > 0)
        center_points = list(zip(*np.where(skeleton)))
        center_points = [(int(y), int(x)) for x, y in center_points]  # (x, y)格式
        
        if len(center_points) < 10:
            return None
        
        # 2. 排序中心点（沿线方向）
        center_points = self._sort_points_along_line(center_points)
        
        # 3. 计算主方向
        main_direction = self._calculate_main_direction(center_points)
        
        # 4. 提取边缘点
        edge_points_left, edge_points_right = self._extract_edge_points(
            lane_mask, center_points, main_direction
        )
        
        # 5. 计算平均宽度
        average_width = self._calculate_average_width(
            center_points, edge_points_left, edge_points_right
        )
        
        # 6. 计算曲率
        curvature = self._calculate_curvature(center_points)
        
        return SolidLineGeometry(
            center_points=center_points,
            edge_points_left=edge_points_left,
            edge_points_right=edge_points_right,
            average_width=average_width,
            main_direction=main_direction,
            curvature=curvature
        )
    
    def _sort_points_along_line(self, points: List[Tuple[int, int]]) -> List[Tuple[int, int]]:
        """沿线方向排序点"""
        if len(points) < 2:
            return points
        
        # 使用最近邻方法排序
        sorted_points = [points[0]]
        remaining_points = points[1:]
        
        while remaining_points:
            current_point = sorted_points[-1]
            distances = [np.sqrt((p[0] - current_point[0])**2 + (p[1] - current_point[1])**2) 
                        for p in remaining_points]
            nearest_idx = np.argmin(distances)
            sorted_points.append(remaining_points.pop(nearest_idx))
        
        return sorted_points
    
    def _calculate_main_direction(self, center_points: List[Tuple[int, int]]) -> np.ndarray:
        """计算主方向向量"""
        if len(center_points) < 2:
            return np.array([1.0, 0.0])
        
        # 使用PCA计算主方向
        points_array = np.array(center_points)
        mean_point = np.mean(points_array, axis=0)
        centered_points = points_array - mean_point
        
        # 计算协方差矩阵
        cov_matrix = np.cov(centered_points.T)
        eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)
        
        # 主方向是最大特征值对应的特征向量
        main_direction = eigenvectors[:, np.argmax(eigenvalues)]
        
        # 归一化
        return main_direction / np.linalg.norm(main_direction)
    
    def _extract_edge_points(self, lane_mask: np.ndarray, 
                           center_points: List[Tuple[int, int]], 
                           main_direction: np.ndarray) -> Tuple[List[Tuple[int, int]], List[Tuple[int, int]]]:
        """提取边缘点"""
        # 计算垂直方向
        perpendicular = np.array([-main_direction[1], main_direction[0]])
        
        edge_points_left = []
        edge_points_right = []
        
        # 对每个中心点，沿垂直方向寻找边缘
        for center_x, center_y in center_points:
            # 向左搜索边缘
            for dist in range(1, 20):
                left_x = int(center_x + perpendicular[0] * dist)
                left_y = int(center_y + perpendicular[1] * dist)
                
                if (0 <= left_x < lane_mask.shape[1] and 
                    0 <= left_y < lane_mask.shape[0]):
                    if lane_mask[left_y, left_x] == 0:  # 找到边缘
                        edge_points_left.append((left_x - int(perpendicular[0]), 
                                               left_y - int(perpendicular[1])))
                        break
            
            # 向右搜索边缘
            for dist in range(1, 20):
                right_x = int(center_x - perpendicular[0] * dist)
                right_y = int(center_y - perpendicular[1] * dist)
                
                if (0 <= right_x < lane_mask.shape[1] and 
                    0 <= right_y < lane_mask.shape[0]):
                    if lane_mask[right_y, right_x] == 0:  # 找到边缘
                        edge_points_right.append((right_x + int(perpendicular[0]), 
                                                right_y + int(perpendicular[1])))
                        break
        
        return edge_points_left, edge_points_right
    
    def _calculate_average_width(self, center_points: List[Tuple[int, int]],
                               edge_points_left: List[Tuple[int, int]],
                               edge_points_right: List[Tuple[int, int]]) -> float:
        """计算平均宽度"""
        widths = []
        
        min_len = min(len(center_points), len(edge_points_left), len(edge_points_right))
        
        for i in range(min_len):
            center = np.array(center_points[i])
            left = np.array(edge_points_left[i])
            right = np.array(edge_points_right[i])
            
            width = np.linalg.norm(left - right)
            widths.append(width)
        
        return np.mean(widths) if widths else 5.0
    
    def _calculate_curvature(self, center_points: List[Tuple[int, int]]) -> float:
        """计算曲率"""
        if len(center_points) < 3:
            return 0.0
        
        curvatures = []
        
        for i in range(1, len(center_points) - 1):
            p1 = np.array(center_points[i-1])
            p2 = np.array(center_points[i])
            p3 = np.array(center_points[i+1])
            
            # 计算三点曲率
            v1 = p2 - p1
            v2 = p3 - p2
            
            cross_product = np.cross(v1, v2)
            norm_v1 = np.linalg.norm(v1)
            norm_v2 = np.linalg.norm(v2)
            
            if norm_v1 > 0 and norm_v2 > 0:
                curvature = abs(cross_product) / (norm_v1 * norm_v2)
                curvatures.append(curvature)
        
        return np.mean(curvatures) if curvatures else 0.0
