# 基于结构规则的遮挡恢复系统配置文件

# 数据路径配置
data:
  images_dir: "data/images"
  masks_dir: "data/masks"
  annotations_dir: "data/annotations"
  results_dir: "data/results"

# 标线分类配置
lane_classes:
  lines:
    - "solid_line"       # 0 - 单实线
    - "dashed_line"      # 1 - 单虚线
    - "double_line"      # 2 - 双实线
    - "stop_line"        # 3 - 停止线
    - "slow_line"        # 4 - 减速线
  arrows:
    - "arrow_straight"   # 5 - 直行箭头
    - "arrow_left"       # 6 - 左转箭头
    - "arrow_right"      # 7 - 右转箭头
    - "arrow_uturn"      # 8 - 掉头箭头
    - "arrow_straight_left"    # 9 - 直行+左转箭头
    - "arrow_straight_right"   # 10 - 直行+右转箭头
    - "arrow_straight_uturn"   # 11 - 直行+掉头箭头
    - "arrow_left_uturn"       # 12 - 左转+掉头箭头
  symbols:
    - "zebra"            # 13 - 人行横道（斑马线）
    - "text"             # 14 - 道路文字及数字
    - "symbol_misc"      # 15 - 其他符号
    - "diverge_area"     # 16 - 导流区
    - "grid_area"        # 17 - 网格区（禁止停车区）
    - "left_right"       # 18 - 左右转箭头

# 像素值映射
pixel_mapping:
  # 非标线类（需要过滤）
  non_lane:
    car: 1
    human: 2
  # 标线类
  lane:
    solid_line: 3
    dashed_line: 4
    double_line: 5
    stop_line: 6
    arrow_straight: 7
    diverge_area: 8
    grid_area: 9
    left_right: 10

# 遮挡检测配置
occlusion_detection:
  overlap_threshold: 0.3    # 重合度阈值
  min_occlusion_area: 50    # 最小遮挡面积(像素)
  confidence_threshold: 0.7  # 置信度阈值

# 规则推理配置
rule_engine:
  max_inference_distance: 100  # 最大推理距离(像素)
  geometric_tolerance: 5       # 几何容差(像素)
  semantic_weight: 0.7         # 语义权重
  geometric_weight: 0.3        # 几何权重

# 结构补全配置
structure_completion:
  interpolation_method: "cubic"  # 插值方法
  smoothing_factor: 0.1         # 平滑因子
  max_gap_length: 200           # 最大补全间隙长度(像素)

# 可视化配置
visualization:
  line_thickness: 2
  colors:
    original: [0, 255, 0]      # 绿色 - 原始标线
    occluded: [255, 0, 0]      # 红色 - 遮挡区域
    completed: [0, 0, 255]     # 蓝色 - 补全区域
    confidence_low: [255, 255, 0]   # 黄色 - 低置信度
    confidence_high: [0, 255, 255]  # 青色 - 高置信度

# 评估配置
evaluation:
  metrics:
    - "completeness"     # 完整性
    - "accuracy"         # 准确性
    - "consistency"      # 一致性
    - "robustness"       # 鲁棒性
  iou_threshold: 0.5     # IoU阈值
  pixel_tolerance: 3     # 像素容差
