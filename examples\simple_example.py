"""
简单示例：演示如何使用遮挡恢复系统
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import cv2
from src.models.mask_model import LaneMask, LaneType, OcclusionType
from src.core.occlusion_detector import OcclusionDetector
from src.core.rule_engine import RuleEngine
from src.core.structure_completer import StructureCompleter
from src.utils.visualization import Visualizer


def create_synthetic_data():
    """创建合成测试数据"""
    # 创建一个640x480的测试图像
    height, width = 480, 640
    
    # 创建背景图像（模拟道路）
    image = np.ones((height, width, 3), dtype=np.uint8) * 128  # 灰色背景
    
    # 添加一些道路纹理
    cv2.rectangle(image, (0, height//3), (width, 2*height//3), (100, 100, 100), -1)
    
    # 创建标线mask
    lane_masks = {}
    
    # 1. 单实线（水平）
    single_line = np.zeros((height, width), dtype=np.uint8)
    cv2.line(single_line, (50, height//2), (width-50, height//2), 1, 5)
    lane_masks['single_line'] = single_line
    
    # 2. 虚线（水平，上方）
    dashed_line = np.zeros((height, width), dtype=np.uint8)
    for x in range(50, width-50, 40):  # 虚线模式：20像素线段，20像素间隔
        cv2.line(dashed_line, (x, height//2 - 50), (x+20, height//2 - 50), 1, 3)
    lane_masks['dashed_line'] = dashed_line
    
    # 3. 左转箭头
    arrow_mask = np.zeros((height, width), dtype=np.uint8)
    # 简化的箭头形状
    arrow_points = np.array([
        [200, height//2 + 80],
        [220, height//2 + 60],
        [210, height//2 + 60],
        [210, height//2 + 40],
        [190, height//2 + 40],
        [190, height//2 + 60],
        [180, height//2 + 60]
    ], np.int32)
    cv2.fillPoly(arrow_mask, [arrow_points], 1)
    lane_masks['left_arrow'] = arrow_mask
    
    # 创建遮挡物mask
    occlusion_masks = {}
    
    # 1. 车辆遮挡（矩形）
    vehicle_mask = np.zeros((height, width), dtype=np.uint8)
    cv2.rectangle(vehicle_mask, (300, height//2 - 30), (450, height//2 + 30), 1, -1)
    occlusion_masks['vehicle'] = vehicle_mask
    
    # 2. 行人遮挡（椭圆）
    pedestrian_mask = np.zeros((height, width), dtype=np.uint8)
    cv2.ellipse(pedestrian_mask, (180, height//2 + 70), (25, 40), 0, 0, 360, 1, -1)
    occlusion_masks['pedestrian'] = pedestrian_mask
    
    # 定义类型
    lane_types = {
        'single_line': LaneType.SINGLE_SOLID,
        'dashed_line': LaneType.DASHED,
        'left_arrow': LaneType.LEFT_TURN
    }
    
    occlusion_types = {
        'vehicle': OcclusionType.DYNAMIC,
        'pedestrian': OcclusionType.DYNAMIC
    }
    
    return image, lane_masks, lane_types, occlusion_masks, occlusion_types


def run_simple_example():
    """运行简单示例"""
    print("=== 遮挡恢复系统简单示例 ===")
    
    # 1. 创建测试数据
    print("1. 创建合成测试数据...")
    image, lane_masks, lane_types, occlusion_masks, occlusion_types = create_synthetic_data()
    
    # 2. 初始化系统组件
    print("2. 初始化系统组件...")
    detector = OcclusionDetector()
    engine = RuleEngine()
    completer = StructureCompleter()
    visualizer = Visualizer()
    
    # 3. 创建标线mask对象
    print("3. 创建标线mask对象...")
    lane_mask = LaneMask(image.shape[:2])
    
    # 添加标线段
    for name, mask in lane_masks.items():
        lane_type = lane_types[name]
        lane_id = lane_mask.add_lane_segment(lane_type, mask)
        print(f"   添加标线段: {name} (ID: {lane_id}, 类型: {lane_type.value})")
    
    # 4. 检测遮挡
    print("4. 检测遮挡...")
    occlusion_analyses = detector.detect_occlusions(
        lane_mask, occlusion_masks, occlusion_types
    )
    
    print(f"   检测到 {len(occlusion_analyses)} 个遮挡区域")
    for analysis in occlusion_analyses:
        print(f"   - 标线 {analysis.lane_id} 被遮挡 {analysis.overlap_ratio:.2%}, "
              f"置信度: {analysis.confidence:.3f}")
    
    # 过滤显著遮挡
    significant_occlusions = detector.filter_significant_occlusions(occlusion_analyses)
    print(f"   其中 {len(significant_occlusions)} 个为显著遮挡")
    
    # 5. 创建上下文信息
    print("5. 创建推理上下文...")
    contexts = []
    for analysis in significant_occlusions:
        lane_segment = lane_mask.lane_segments[analysis.lane_id]
        context = detector.create_occlusion_context(lane_segment, analysis, lane_mask)
        contexts.append(context)
        print(f"   标线 {analysis.lane_id}: 类型={context['lane_type'].value}, "
              f"遮挡比例={context['occlusion_ratio']:.2%}")
    
    # 6. 规则推理
    print("6. 执行规则推理...")
    inference_results = engine.infer_completions(lane_mask, significant_occlusions, contexts)
    
    print(f"   生成 {len(inference_results)} 个推理结果")
    for result in inference_results:
        print(f"   - 规则 {result.rule_id}: 标线 {result.lane_id}, "
              f"置信度: {result.confidence:.3f}")
    
    # 7. 结构补全
    print("7. 执行结构补全...")
    completion_result = completer.complete_structure(lane_mask, inference_results)
    
    print("   补全统计:")
    stats = completion_result.statistics
    print(f"   - 总推理数: {stats['total_inferences']}")
    print(f"   - 成功补全: {stats['successful_completions']}")
    print(f"   - 失败补全: {stats['failed_completions']}")
    print(f"   - 补全像素: {stats['total_completed_pixels']}")
    print(f"   - 平均置信度: {stats['average_confidence']:.3f}")
    
    # 8. 生成可视化
    print("8. 生成可视化结果...")
    
    # 创建对比视图
    comparison_view = visualizer.create_comparison_view(
        image,
        completion_result.original_mask,
        completion_result.completed_mask,
        completion_result.completion_regions,
        completion_result.confidence_map
    )
    
    # 创建逐步处理视图
    step_view = visualizer.create_step_by_step_view(
        image, lane_mask, significant_occlusions, inference_results
    )
    
    # 创建统计图表
    stats_plot = visualizer.create_statistics_plot(stats)
    
    # 9. 保存结果
    print("9. 保存结果...")
    output_dir = "examples/output"
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存图像
    cv2.imwrite(f"{output_dir}/original_image.jpg", image)
    cv2.imwrite(f"{output_dir}/comparison_view.jpg", comparison_view)
    cv2.imwrite(f"{output_dir}/step_by_step_view.jpg", step_view)
    cv2.imwrite(f"{output_dir}/statistics_plot.jpg", stats_plot)
    
    # 保存mask
    cv2.imwrite(f"{output_dir}/original_mask.png", 
               completion_result.original_mask * 255)
    cv2.imwrite(f"{output_dir}/completed_mask.png", 
               completion_result.completed_mask * 255)
    cv2.imwrite(f"{output_dir}/completion_regions.png", 
               completion_result.completion_regions * 255)
    
    # 生成详细报告
    report = completer.generate_completion_report(completion_result)
    
    # 保存报告
    import json
    with open(f"{output_dir}/detailed_report.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"   结果已保存到: {output_dir}")
    
    # 10. 显示总结
    print("\n=== 处理总结 ===")
    print(f"原始标线像素: {report['summary']['original_pixels']}")
    print(f"补全后像素: {report['summary']['completed_pixels']}")
    print(f"新增像素: {report['summary']['completion_pixels']}")
    print(f"补全比例: {report['summary']['completion_ratio']:.2%}")
    print(f"总体改进: {report['summary']['total_improvement']:.2%}")
    
    print("\n示例运行完成！")
    return completion_result, report


def demonstrate_rule_matching():
    """演示规则匹配过程"""
    print("\n=== 规则匹配演示 ===")
    
    # 创建规则引擎
    engine = RuleEngine()
    
    # 显示可用规则
    print("可用规则:")
    for rule in engine.rule_set.rules:
        print(f"  - {rule.rule_id}: {rule.name}")
        print(f"    类型: {rule.rule_type.value}")
        print(f"    优先级: {rule.priority}")
        print(f"    条件数: {len(rule.conditions)}")
        print()
    
    # 测试不同的上下文
    test_contexts = [
        {
            'lane_type': LaneType.SINGLE_SOLID,
            'occlusion_ratio': 0.5,
            'visible_length': 50,
            'direction': (1.0, 0.0)
        },
        {
            'lane_type': LaneType.DASHED,
            'occlusion_ratio': 0.3,
            'visible_length': 80,
            'direction': (1.0, 0.0)
        },
        {
            'lane_type': LaneType.LEFT_TURN,
            'occlusion_ratio': 0.4,
            'visible_length': 30,
            'direction': (0.0, 1.0)
        }
    ]
    
    print("规则匹配测试:")
    for i, context in enumerate(test_contexts):
        print(f"\n测试上下文 {i+1}:")
        print(f"  标线类型: {context['lane_type'].value}")
        print(f"  遮挡比例: {context['occlusion_ratio']:.1%}")
        print(f"  可见长度: {context['visible_length']}")
        
        # 查找匹配规则
        matching_rules = engine.rule_set.find_matching_rules(context)
        
        if matching_rules:
            print(f"  匹配规则 ({len(matching_rules)} 个):")
            for rule, confidence in matching_rules:
                print(f"    - {rule.rule_id}: {rule.name} (置信度: {confidence:.3f})")
        else:
            print("  无匹配规则")


if __name__ == "__main__":
    try:
        # 运行主示例
        completion_result, report = run_simple_example()
        
        # 演示规则匹配
        demonstrate_rule_matching()
        
        print("\n所有示例运行完成！")
        
    except Exception as e:
        print(f"示例运行出错: {e}")
        import traceback
        traceback.print_exc()
