"""
高级推理规则模型
基于用户专业理解重新设计的规则系统
"""

import numpy as np
import cv2
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, field
from enum import Enum

from .mask_model import LaneType, LaneSegment, OcclusionRegion


@dataclass
class GeometricProperties:
    """几何属性"""
    width: float
    length: float
    curvature: float
    orientation: float
    edge_points: List[Tuple[int, int]]
    center_line: List[Tuple[int, int]]


@dataclass
class DashedLinePattern:
    """虚线模式"""
    dash_length: float
    gap_length: float
    dash_width: float
    pattern_angle: float
    center_line: List[Tuple[int, int]]
    dash_segments: List[Dict[str, Any]]


@dataclass
class ParallelLineGroup:
    """平行线组"""
    lines: List[LaneSegment]
    spacing: float
    orientation: float
    length_range: Tuple[float, float]
    width_range: Tuple[float, float]


class AdvancedCompletionAction:
    """高级补全动作"""

    def __init__(self, action_type: str, parameters: Dict[str, Any]):
        self.action_type = action_type
        self.parameters = parameters

    def execute(self,
                lane_segment: LaneSegment,
                occlusion_region: OcclusionRegion,
                context: Dict[str, Any]) -> np.ndarray:
        """执行补全动作"""

        if self.action_type == "solid_line_completion":
            return self._complete_solid_line(lane_segment, occlusion_region, context)
        elif self.action_type == "dashed_line_completion":
            return self._complete_dashed_line(lane_segment, occlusion_region, context)
        elif self.action_type == "stop_line_completion":
            return self._complete_stop_line(lane_segment, occlusion_region, context)
        elif self.action_type == "zebra_completion":
            return self._complete_zebra_crossing(lane_segment, occlusion_region, context)
        elif self.action_type == "grid_area_completion":
            return self._complete_grid_area(lane_segment, occlusion_region, context)
        elif self.action_type == "diverge_area_completion":
            return self._complete_diverge_area(lane_segment, occlusion_region, context)
        else:
            return np.zeros_like(occlusion_region.mask)

    def _complete_solid_line(self,
                           lane_segment: LaneSegment,
                           occlusion_region: OcclusionRegion,
                           context: Dict[str, Any]) -> np.ndarray:
        """
        实线补全：连续、可弯曲的长矩形
        通过检测遮挡两端的边缘，计算曲度，连接边界点
        """
        result_mask = np.zeros_like(occlusion_region.mask)

        # 1. 获取可见部分
        visible_mask = np.logical_and(lane_segment.mask, ~occlusion_region.mask)
        if not np.any(visible_mask):
            return result_mask

        # 2. 分析几何属性
        geom_props = self._analyze_geometric_properties(visible_mask)
        if geom_props is None:
            return result_mask

        # 3. 检测遮挡区域两端的边缘点
        left_edge, right_edge = self._detect_occlusion_edges(
            visible_mask, occlusion_region.mask, geom_props
        )

        if left_edge is None or right_edge is None:
            return result_mask

        # 4. 计算曲度并生成连接路径
        connection_path = self._generate_curved_connection(
            left_edge, right_edge, geom_props
        )

        # 5. 绘制补全区域
        result_mask = self._draw_solid_connection(
            result_mask, connection_path, geom_props.width
        )

        # 6. 只保留在遮挡区域内的部分
        result_mask = np.logical_and(result_mask, occlusion_region.mask)

        return result_mask.astype(np.uint8)

    def _complete_dashed_line(self,
                            lane_segment: LaneSegment,
                            occlusion_region: OcclusionRegion,
                            context: Dict[str, Any]) -> np.ndarray:
        """
        虚线补全：连续、间断、可弯曲的矩形
        分析虚线模式：段长、间隔、宽度的一致性
        """
        result_mask = np.zeros_like(occlusion_region.mask)

        # 1. 获取可见部分
        visible_mask = np.logical_and(lane_segment.mask, ~occlusion_region.mask)
        if not np.any(visible_mask):
            return result_mask

        # 2. 分析虚线模式
        pattern = self._analyze_dashed_pattern(visible_mask)
        if pattern is None:
            return result_mask

        # 3. 在遮挡区域内生成虚线模式
        result_mask = self._generate_dashed_pattern_in_occlusion(
            occlusion_region.mask, pattern, visible_mask
        )

        return result_mask.astype(np.uint8)

    def _complete_stop_line(self,
                          lane_segment: LaneSegment,
                          occlusion_region: OcclusionRegion,
                          context: Dict[str, Any]) -> np.ndarray:
        """
        停止线补全：连续、可弯曲的长矩形
        一般垂直于虚实线，首尾终止于虚实线
        """
        result_mask = np.zeros_like(occlusion_region.mask)

        # 1. 获取可见部分
        visible_mask = np.logical_and(lane_segment.mask, ~occlusion_region.mask)
        if not np.any(visible_mask):
            return result_mask

        # 2. 检测相邻的虚实线（从context中获取）
        adjacent_lines = context.get('adjacent_lines', [])

        # 3. 分析停止线的几何属性
        geom_props = self._analyze_geometric_properties(visible_mask)
        if geom_props is None:
            return result_mask

        # 4. 确保垂直关系和边界约束
        result_mask = self._complete_perpendicular_line(
            visible_mask, occlusion_region.mask, geom_props, adjacent_lines
        )

        return result_mask.astype(np.uint8)

    def _complete_zebra_crossing(self,
                               lane_segment: LaneSegment,
                               occlusion_region: OcclusionRegion,
                               context: Dict[str, Any]) -> np.ndarray:
        """
        斑马线补全：多条相互平行等长的实线，间隔相等
        """
        result_mask = np.zeros_like(occlusion_region.mask)

        # 1. 获取可见部分
        visible_mask = np.logical_and(lane_segment.mask, ~occlusion_region.mask)
        if not np.any(visible_mask):
            return result_mask

        # 2. 分析平行线组
        parallel_group = self._analyze_parallel_lines(visible_mask)
        if parallel_group is None:
            return result_mask

        # 3. 在遮挡区域内补全平行线
        result_mask = self._complete_parallel_lines_in_occlusion(
            occlusion_region.mask, parallel_group
        )

        return result_mask.astype(np.uint8)

    def _complete_grid_area(self,
                          lane_segment: LaneSegment,
                          occlusion_region: OcclusionRegion,
                          context: Dict[str, Any]) -> np.ndarray:
        """
        网格区补全：X型填充，边界可能不规则
        填充线首尾在边界上且不超出边界
        """
        result_mask = np.zeros_like(occlusion_region.mask)

        # 1. 获取可见部分
        visible_mask = np.logical_and(lane_segment.mask, ~occlusion_region.mask)
        if not np.any(visible_mask):
            return result_mask

        # 2. 检测边界
        boundary = self._detect_area_boundary(visible_mask)
        if boundary is None:
            return result_mask

        # 3. 分析填充模式
        fill_pattern = self._analyze_grid_fill_pattern(visible_mask, boundary)

        # 4. 在遮挡区域内生成网格填充
        result_mask = self._generate_grid_fill_in_occlusion(
            occlusion_region.mask, boundary, fill_pattern
        )

        return result_mask.astype(np.uint8)

    def _complete_diverge_area(self,
                             lane_segment: LaneSegment,
                             occlusion_region: OcclusionRegion,
                             context: Dict[str, Any]) -> np.ndarray:
        """
        导流区补全：V形或菱形填充，平行线填充
        """
        result_mask = np.zeros_like(occlusion_region.mask)

        # 1. 获取可见部分
        visible_mask = np.logical_and(lane_segment.mask, ~occlusion_region.mask)
        if not np.any(visible_mask):
            return result_mask

        # 2. 分析导流区形状和填充模式
        diverge_pattern = self._analyze_diverge_pattern(visible_mask)
        if diverge_pattern is None:
            return result_mask

        # 3. 在遮挡区域内生成导流填充
        result_mask = self._generate_diverge_fill_in_occlusion(
            occlusion_region.mask, diverge_pattern
        )

        return result_mask.astype(np.uint8)

    def _analyze_geometric_properties(self, mask: np.ndarray) -> Optional[GeometricProperties]:
        """分析几何属性：宽度、长度、曲度、方向"""
        if not np.any(mask):
            return None

        # 获取轮廓
        contours, _ = cv2.findContours(mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if len(contours) == 0:
            return None

        main_contour = max(contours, key=cv2.contourArea)

        # 计算最小外接矩形
        rect = cv2.minAreaRect(main_contour)
        width = min(rect[1])
        length = max(rect[1])
        orientation = rect[2]

        # 计算中心线
        from skimage.morphology import skeletonize
        skeleton = skeletonize(mask)
        center_points = np.where(skeleton)
        center_line = list(zip(center_points[1], center_points[0]))  # (x, y)

        # 计算曲度（简化版本）
        curvature = self._calculate_curvature(center_line)

        # 检测边缘点
        edge_points = self._extract_edge_points(main_contour)

        return GeometricProperties(
            width=width,
            length=length,
            curvature=curvature,
            orientation=orientation,
            edge_points=edge_points,
            center_line=center_line
        )

    def _calculate_curvature(self, points: List[Tuple[int, int]]) -> float:
        """计算曲线的曲度"""
        if len(points) < 3:
            return 0.0

        # 简化的曲度计算：使用三点法
        curvatures = []
        for i in range(1, len(points) - 1):
            p1, p2, p3 = points[i-1], points[i], points[i+1]

            # 计算角度变化
            v1 = (p2[0] - p1[0], p2[1] - p1[1])
            v2 = (p3[0] - p2[0], p3[1] - p2[1])

            # 避免除零
            len1 = np.sqrt(v1[0]**2 + v1[1]**2)
            len2 = np.sqrt(v2[0]**2 + v2[1]**2)

            if len1 > 0 and len2 > 0:
                cos_angle = (v1[0]*v2[0] + v1[1]*v2[1]) / (len1 * len2)
                cos_angle = np.clip(cos_angle, -1, 1)
                angle = np.arccos(cos_angle)
                curvatures.append(angle)

        return np.mean(curvatures) if curvatures else 0.0

    def _extract_edge_points(self, contour: np.ndarray) -> List[Tuple[int, int]]:
        """提取轮廓的关键边缘点"""
        # 简化版本：返回轮廓的所有点
        return [(point[0][0], point[0][1]) for point in contour]

    def _detect_occlusion_edges(self,
                              visible_mask: np.ndarray,
                              occlusion_mask: np.ndarray,
                              geom_props: GeometricProperties) -> Tuple[Optional[Dict], Optional[Dict]]:
        """检测遮挡区域两端的边缘"""
        # 找到遮挡区域的边界
        occlusion_coords = np.where(occlusion_mask)
        if len(occlusion_coords[0]) == 0:
            return None, None

        # 获取遮挡区域的边界框
        y_min, y_max = np.min(occlusion_coords[0]), np.max(occlusion_coords[0])
        x_min, x_max = np.min(occlusion_coords[1]), np.max(occlusion_coords[1])

        # 在遮挡区域附近寻找可见标线的边缘
        margin = 20

        # 左边缘
        left_region = visible_mask[max(0, y_min-margin):y_max+margin,
                                 max(0, x_min-margin):x_min+margin]
        left_edge = self._find_edge_in_region(left_region, 'right')

        # 右边缘
        right_region = visible_mask[max(0, y_min-margin):y_max+margin,
                                  x_max-margin:min(visible_mask.shape[1], x_max+margin)]
        right_edge = self._find_edge_in_region(right_region, 'left')

        return left_edge, right_edge

    def _find_edge_in_region(self, region: np.ndarray, edge_type: str) -> Optional[Dict]:
        """在区域内寻找边缘"""
        if not np.any(region):
            return None

        coords = np.where(region)
        if len(coords[0]) == 0:
            return None

        if edge_type == 'right':
            # 找最右边的点
            max_x_idx = np.argmax(coords[1])
            edge_point = (coords[1][max_x_idx], coords[0][max_x_idx])
        else:  # 'left'
            # 找最左边的点
            min_x_idx = np.argmin(coords[1])
            edge_point = (coords[1][min_x_idx], coords[0][min_x_idx])

        return {'point': edge_point, 'region': region}

    def _generate_curved_connection(self,
                                  left_edge: Dict,
                                  right_edge: Dict,
                                  geom_props: GeometricProperties) -> List[Tuple[int, int]]:
        """生成弯曲连接路径"""
        start_point = left_edge['point']
        end_point = right_edge['point']

        # 简化版本：使用贝塞尔曲线或直线连接
        if geom_props.curvature < 0.1:  # 直线
            return self._generate_straight_line(start_point, end_point)
        else:  # 曲线
            return self._generate_bezier_curve(start_point, end_point, geom_props.curvature)

    def _generate_straight_line(self, start: Tuple[int, int], end: Tuple[int, int]) -> List[Tuple[int, int]]:
        """生成直线路径"""
        points = []
        x1, y1 = start
        x2, y2 = end

        steps = max(abs(x2 - x1), abs(y2 - y1))
        if steps == 0:
            return [start]

        for i in range(steps + 1):
            t = i / steps
            x = int(x1 + t * (x2 - x1))
            y = int(y1 + t * (y2 - y1))
            points.append((x, y))

        return points

    def _generate_bezier_curve(self, start: Tuple[int, int], end: Tuple[int, int], curvature: float) -> List[Tuple[int, int]]:
        """生成贝塞尔曲线路径"""
        # 简化的二次贝塞尔曲线
        x1, y1 = start
        x2, y2 = end

        # 控制点
        mid_x = (x1 + x2) / 2
        mid_y = (y1 + y2) / 2

        # 根据曲度调整控制点
        offset = curvature * 50  # 调整系数
        cx = mid_x
        cy = mid_y + offset

        points = []
        steps = 20
        for i in range(steps + 1):
            t = i / steps
            # 二次贝塞尔曲线公式
            x = (1-t)**2 * x1 + 2*(1-t)*t * cx + t**2 * x2
            y = (1-t)**2 * y1 + 2*(1-t)*t * cy + t**2 * y2
            points.append((int(x), int(y)))

        return points

    def _draw_solid_connection(self, mask: np.ndarray, path: List[Tuple[int, int]], width: float) -> np.ndarray:
        """绘制实线连接"""
        result = mask.copy()

        if len(path) < 2:
            return result

        line_width = max(1, int(width))

        for i in range(len(path) - 1):
            pt1 = path[i]
            pt2 = path[i + 1]
            cv2.line(result, pt1, pt2, 1, line_width)

        return result

    def _analyze_dashed_pattern(self, mask: np.ndarray) -> Optional[DashedLinePattern]:
        """
        分析虚线模式：段长、间隔、宽度的一致性
        利用小段虚线不平行和中心在一条直线来区分
        """
        if not np.any(mask):
            return None

        # 1. 获取连通组件（每个虚线段）
        num_labels, labels = cv2.connectedComponents(mask.astype(np.uint8))
        if num_labels < 2:  # 至少需要2个虚线段
            return None

        dash_segments = []

        # 2. 分析每个虚线段
        for label in range(1, num_labels):
            segment_mask = (labels == label).astype(np.uint8)
            segment_props = self._analyze_dash_segment(segment_mask)
            if segment_props:
                dash_segments.append(segment_props)

        if len(dash_segments) < 2:
            return None

        # 3. 计算平均属性
        avg_length = np.mean([seg['length'] for seg in dash_segments])
        avg_width = np.mean([seg['width'] for seg in dash_segments])
        avg_angle = np.mean([seg['angle'] for seg in dash_segments])

        # 4. 计算间隔
        centers = [seg['center'] for seg in dash_segments]
        centers.sort(key=lambda p: p[0])  # 按x坐标排序

        gaps = []
        for i in range(len(centers) - 1):
            gap = np.sqrt((centers[i+1][0] - centers[i][0])**2 +
                         (centers[i+1][1] - centers[i][1])**2)
            gaps.append(gap)

        avg_gap = np.mean(gaps) if gaps else avg_length

        # 5. 计算中心线
        center_line = self._calculate_dashed_center_line(centers, avg_angle)

        return DashedLinePattern(
            dash_length=avg_length,
            gap_length=avg_gap,
            dash_width=avg_width,
            pattern_angle=avg_angle,
            center_line=center_line,
            dash_segments=dash_segments
        )

    def _analyze_dash_segment(self, segment_mask: np.ndarray) -> Optional[Dict[str, Any]]:
        """分析单个虚线段的属性"""
        contours, _ = cv2.findContours(segment_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if len(contours) == 0:
            return None

        contour = max(contours, key=cv2.contourArea)

        # 计算最小外接矩形
        rect = cv2.minAreaRect(contour)
        center = rect[0]
        size = rect[1]
        angle = rect[2]

        # 长度和宽度
        length = max(size)
        width = min(size)

        return {
            'center': center,
            'length': length,
            'width': width,
            'angle': angle,
            'contour': contour
        }

    def _calculate_dashed_center_line(self, centers: List[Tuple[float, float]], angle: float) -> List[Tuple[int, int]]:
        """计算虚线的中心线"""
        if len(centers) < 2:
            return []

        # 使用线性回归拟合中心线
        x_coords = [c[0] for c in centers]
        y_coords = [c[1] for c in centers]

        # 简化版本：连接所有中心点
        center_line = []
        for i in range(len(centers)):
            center_line.append((int(centers[i][0]), int(centers[i][1])))

        return center_line

    def _generate_dashed_pattern_in_occlusion(self,
                                            occlusion_mask: np.ndarray,
                                            pattern: DashedLinePattern,
                                            visible_mask: np.ndarray) -> np.ndarray:
        """在遮挡区域内生成虚线模式"""
        result = np.zeros_like(occlusion_mask)

        # 1. 确定遮挡区域的范围
        occlusion_coords = np.where(occlusion_mask)
        if len(occlusion_coords[0]) == 0:
            return result

        y_min, y_max = np.min(occlusion_coords[0]), np.max(occlusion_coords[0])
        x_min, x_max = np.min(occlusion_coords[1]), np.max(occlusion_coords[1])

        # 2. 延伸中心线到遮挡区域
        extended_center_line = self._extend_center_line_to_occlusion(
            pattern.center_line, (x_min, y_min, x_max, y_max), pattern.pattern_angle
        )

        # 3. 沿中心线生成虚线段
        current_pos = 0
        is_dash = True

        for i in range(len(extended_center_line) - 1):
            if is_dash:
                # 绘制虚线段
                self._draw_dash_segment(
                    result, extended_center_line[i], extended_center_line[i+1],
                    pattern.dash_width, occlusion_mask
                )
                current_pos += pattern.dash_length
            else:
                # 跳过间隔
                current_pos += pattern.gap_length

            # 切换状态
            if current_pos >= pattern.dash_length + pattern.gap_length:
                is_dash = not is_dash
                current_pos = 0

        return result

    def _extend_center_line_to_occlusion(self,
                                       center_line: List[Tuple[int, int]],
                                       occlusion_bounds: Tuple[int, int, int, int],
                                       angle: float) -> List[Tuple[int, int]]:
        """将中心线延伸到遮挡区域"""
        x_min, y_min, x_max, y_max = occlusion_bounds

        if not center_line:
            # 如果没有中心线，创建一条穿过遮挡区域的线
            center_x = (x_min + x_max) // 2
            center_y = (y_min + y_max) // 2
            return [(x_min, center_y), (x_max, center_y)]

        # 简化版本：在遮挡区域内创建直线段
        extended_line = []

        # 找到与遮挡区域相交的部分
        for x in range(x_min, x_max, 5):  # 每5像素一个点
            if x_min <= x <= x_max:
                # 根据角度计算y坐标
                y = int((y_min + y_max) / 2)  # 简化：使用中心y坐标
                extended_line.append((x, y))

        return extended_line

    def _draw_dash_segment(self,
                         mask: np.ndarray,
                         start: Tuple[int, int],
                         end: Tuple[int, int],
                         width: float,
                         occlusion_mask: np.ndarray):
        """绘制单个虚线段"""
        line_width = max(1, int(width))

        # 创建临时mask
        temp_mask = np.zeros_like(mask)
        cv2.line(temp_mask, start, end, 1, line_width)

        # 只保留在遮挡区域内的部分
        valid_area = np.logical_and(temp_mask, occlusion_mask)
        mask[valid_area] = 1