"""
智能补全执行器
基于几何形状规则的真正补全实现
"""

import numpy as np
import cv2
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import logging

from ..models.mask_model import LaneType
from .intelligent_completion_detector import CompletionTrigger


@dataclass
class CompletionResult:
    """补全结果"""
    trigger: CompletionTrigger
    completed_mask: np.ndarray
    completion_method: str
    confidence: float
    completed_pixels: int


class IntelligentCompletionExecutor:
    """智能补全执行器"""
    
    def __init__(self, config: Dict = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.smoothing_kernel_size = self.config.get('smoothing_kernel_size', 5)
        self.line_width_estimation = self.config.get('line_width_estimation', True)
        self.geometric_validation = self.config.get('geometric_validation', True)
    
    def execute_completions(self, 
                          triggers: List[CompletionTrigger],
                          lane_masks: Dict[str, np.ndarray],
                          lane_types: Dict[str, LaneType]) -> List[CompletionResult]:
        """
        执行智能补全
        
        Args:
            triggers: 补全触发条件列表
            lane_masks: 标线mask字典
            lane_types: 标线类型字典
            
        Returns:
            补全结果列表
        """
        completion_results = []
        
        for i, trigger in enumerate(triggers):
            print(f"\n执行补全 {i+1}/{len(triggers)}: {trigger.lane_type.value}")
            
            try:
                # 根据标线类型选择补全方法
                if trigger.lane_type == LaneType.SOLID_LINE:
                    result = self._complete_solid_line(trigger, lane_masks, lane_types)
                elif trigger.lane_type == LaneType.DASHED_LINE:
                    result = self._complete_dashed_line(trigger, lane_masks, lane_types)
                elif trigger.lane_type == LaneType.DOUBLE_LINE:
                    result = self._complete_double_line(trigger, lane_masks, lane_types)
                elif trigger.lane_type == LaneType.STOP_LINE:
                    result = self._complete_stop_line(trigger, lane_masks, lane_types)
                elif trigger.lane_type == LaneType.GRID_AREA:
                    result = self._complete_grid_area(trigger, lane_masks, lane_types)
                elif trigger.lane_type == LaneType.DIVERGE_AREA:
                    result = self._complete_diverge_area(trigger, lane_masks, lane_types)
                elif trigger.lane_type in [LaneType.ARROW_STRAIGHT, LaneType.ARROW_LEFT, 
                                         LaneType.ARROW_RIGHT, LaneType.LEFT_RIGHT]:
                    result = self._complete_arrow(trigger, lane_masks, lane_types)
                else:
                    # 默认使用实线补全方法
                    result = self._complete_solid_line(trigger, lane_masks, lane_types)
                
                if result and result.completed_pixels > 0:
                    completion_results.append(result)
                    print(f"  ✅ 补全成功: {result.completed_pixels} 像素, "
                          f"方法: {result.completion_method}, 置信度: {result.confidence:.3f}")
                else:
                    print(f"  ❌ 补全失败: 没有生成有效像素")
                    
            except Exception as e:
                print(f"  ❌ 补全执行出错: {e}")
                self.logger.error(f"Completion execution failed: {e}")
        
        return completion_results
    
    def _complete_solid_line(self, 
                           trigger: CompletionTrigger,
                           lane_masks: Dict[str, np.ndarray],
                           lane_types: Dict[str, LaneType]) -> Optional[CompletionResult]:
        """
        实线补全：连续、可弯曲的长矩形
        基于边缘检测和曲度分析
        """
        # 1. 收集同类型标线的几何特征
        same_type_masks = []
        for name, mask in lane_masks.items():
            if lane_types[name] == trigger.lane_type:
                same_type_masks.append(mask)
        
        if not same_type_masks:
            return None
        
        # 2. 分析标线的几何特征
        combined_mask = np.logical_or.reduce(same_type_masks).astype(np.uint8)
        geometry = self._analyze_line_geometry(combined_mask)
        
        if geometry is None:
            return None
        
        print(f"    实线几何分析: 宽度={geometry['width']:.1f}, 方向=({geometry['direction'][0]:.2f}, {geometry['direction'][1]:.2f})")
        
        # 3. 在缺失区域内生成实线
        completed_mask = self._generate_solid_line_in_area(
            trigger.missing_area, geometry
        )
        
        # 4. 应用平滑处理
        if self.smoothing_kernel_size > 0:
            completed_mask = self._apply_smoothing(completed_mask)
        
        # 5. 几何验证
        if self.geometric_validation:
            completed_mask = self._validate_line_geometry(completed_mask, geometry)
        
        completed_pixels = np.sum(completed_mask)
        confidence = min(1.0, completed_pixels / max(np.sum(trigger.missing_area), 1))
        
        return CompletionResult(
            trigger=trigger,
            completed_mask=completed_mask,
            completion_method="solid_line_geometric",
            confidence=confidence,
            completed_pixels=completed_pixels
        )
    
    def _complete_dashed_line(self,
                            trigger: CompletionTrigger,
                            lane_masks: Dict[str, np.ndarray],
                            lane_types: Dict[str, LaneType]) -> Optional[CompletionResult]:
        """
        虚线补全：连续、间断、可弯曲的矩形
        分析虚线模式并在缺失区域重现
        """
        # 1. 收集虚线mask
        dashed_masks = []
        for name, mask in lane_masks.items():
            if lane_types[name] == LaneType.DASHED_LINE:
                dashed_masks.append(mask)
        
        if not dashed_masks:
            return None
        
        # 2. 分析虚线模式
        combined_mask = np.logical_or.reduce(dashed_masks).astype(np.uint8)
        pattern = self._analyze_dashed_pattern(combined_mask)
        
        if pattern is None:
            return None
        
        print(f"    虚线模式分析: 段长={pattern['dash_length']:.1f}, "
              f"间隔={pattern['gap_length']:.1f}, 宽度={pattern['width']:.1f}")
        
        # 3. 在缺失区域内生成虚线模式
        completed_mask = self._generate_dashed_pattern_in_area(
            trigger.missing_area, pattern
        )
        
        # 4. 应用平滑处理
        if self.smoothing_kernel_size > 0:
            completed_mask = self._apply_smoothing(completed_mask)
        
        completed_pixels = np.sum(completed_mask)
        confidence = min(1.0, completed_pixels / max(np.sum(trigger.missing_area), 1))
        
        return CompletionResult(
            trigger=trigger,
            completed_mask=completed_mask,
            completion_method="dashed_line_pattern",
            confidence=confidence,
            completed_pixels=completed_pixels
        )
    
    def _complete_double_line(self,
                            trigger: CompletionTrigger,
                            lane_masks: Dict[str, np.ndarray],
                            lane_types: Dict[str, LaneType]) -> Optional[CompletionResult]:
        """双实线补全：保持平行关系"""
        # 双实线可以使用实线的方法，但需要考虑平行关系
        return self._complete_solid_line(trigger, lane_masks, lane_types)
    
    def _complete_stop_line(self,
                          trigger: CompletionTrigger,
                          lane_masks: Dict[str, np.ndarray],
                          lane_types: Dict[str, LaneType]) -> Optional[CompletionResult]:
        """
        停止线补全：垂直于其他线条的连续线
        """
        # 1. 寻找相邻的实线或虚线来确定垂直方向
        reference_direction = self._find_reference_direction_for_stop_line(lane_masks, lane_types)
        
        # 2. 计算垂直方向
        if reference_direction is not None:
            # 垂直方向
            perpendicular_direction = np.array([-reference_direction[1], reference_direction[0]])
        else:
            # 默认水平方向
            perpendicular_direction = np.array([1.0, 0.0])
        
        print(f"    停止线方向: ({perpendicular_direction[0]:.2f}, {perpendicular_direction[1]:.2f})")
        
        # 3. 生成停止线
        geometry = {
            'direction': perpendicular_direction,
            'width': 8,  # 停止线通常较宽
            'length': 100
        }
        
        completed_mask = self._generate_solid_line_in_area(trigger.missing_area, geometry)
        
        completed_pixels = np.sum(completed_mask)
        confidence = min(1.0, completed_pixels / max(np.sum(trigger.missing_area), 1))
        
        return CompletionResult(
            trigger=trigger,
            completed_mask=completed_mask,
            completion_method="stop_line_perpendicular",
            confidence=confidence,
            completed_pixels=completed_pixels
        )
    
    def _complete_grid_area(self,
                          trigger: CompletionTrigger,
                          lane_masks: Dict[str, np.ndarray],
                          lane_types: Dict[str, LaneType]) -> Optional[CompletionResult]:
        """
        网格区补全：X型填充，边界约束
        """
        # 1. 分析现有网格区的模式
        grid_masks = []
        for name, mask in lane_masks.items():
            if lane_types[name] == LaneType.GRID_AREA:
                grid_masks.append(mask)
        
        if grid_masks:
            combined_mask = np.logical_or.reduce(grid_masks).astype(np.uint8)
            grid_pattern = self._analyze_grid_pattern(combined_mask)
        else:
            # 默认网格模式
            grid_pattern = {'spacing': 15, 'line_width': 2}
        
        print(f"    网格模式: 间距={grid_pattern['spacing']}, 线宽={grid_pattern['line_width']}")
        
        # 2. 在缺失区域内生成网格
        completed_mask = self._generate_grid_pattern_in_area(trigger.missing_area, grid_pattern)
        
        completed_pixels = np.sum(completed_mask)
        confidence = min(1.0, completed_pixels / max(np.sum(trigger.missing_area), 1))
        
        return CompletionResult(
            trigger=trigger,
            completed_mask=completed_mask,
            completion_method="grid_area_pattern",
            confidence=confidence,
            completed_pixels=completed_pixels
        )
    
    def _complete_diverge_area(self,
                             trigger: CompletionTrigger,
                             lane_masks: Dict[str, np.ndarray],
                             lane_types: Dict[str, LaneType]) -> Optional[CompletionResult]:
        """导流区补全：V形或菱形填充"""
        # 导流区使用类似网格的方法，但是斜线模式
        return self._complete_grid_area(trigger, lane_masks, lane_types)
    
    def _complete_arrow(self,
                       trigger: CompletionTrigger,
                       lane_masks: Dict[str, np.ndarray],
                       lane_types: Dict[str, LaneType]) -> Optional[CompletionResult]:
        """箭头补全：基于形状分析"""
        # 箭头补全比较复杂，这里简化为矩形区域填充
        completed_mask = trigger.missing_area.copy()
        
        completed_pixels = np.sum(completed_mask)
        confidence = 0.8  # 箭头补全的置信度相对较低
        
        return CompletionResult(
            trigger=trigger,
            completed_mask=completed_mask,
            completion_method="arrow_shape",
            confidence=confidence,
            completed_pixels=completed_pixels
        )
    
    def _analyze_line_geometry(self, line_mask: np.ndarray) -> Optional[Dict]:
        """分析线条的几何特征"""
        if not np.any(line_mask):
            return None
        
        # 1. 计算主方向
        coords = np.where(line_mask)
        if len(coords[0]) < 10:
            return None
        
        points = np.column_stack((coords[1], coords[0]))  # (x, y)
        mean_point = np.mean(points, axis=0)
        centered_points = points - mean_point
        
        # PCA计算主方向
        cov_matrix = np.cov(centered_points.T)
        eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)
        main_direction = eigenvectors[:, np.argmax(eigenvalues)]
        
        # 2. 估计线宽
        contours, _ = cv2.findContours(line_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if len(contours) == 0:
            return None
        
        main_contour = max(contours, key=cv2.contourArea)
        rect = cv2.minAreaRect(main_contour)
        width = min(rect[1])
        
        return {
            'direction': main_direction,
            'width': max(3, width),
            'center': mean_point,
            'length': max(rect[1])
        }
    
    def _generate_solid_line_in_area(self, area_mask: np.ndarray, geometry: Dict) -> np.ndarray:
        """在指定区域内生成实线"""
        result = np.zeros_like(area_mask)
        
        # 获取区域边界
        coords = np.where(area_mask)
        if len(coords[0]) == 0:
            return result
        
        y_min, y_max = np.min(coords[0]), np.max(coords[0])
        x_min, x_max = np.min(coords[1]), np.max(coords[1])
        
        # 计算区域中心
        center = np.array([(x_min + x_max) / 2, (y_min + y_max) / 2])
        
        # 沿主方向绘制线段
        direction = geometry['direction']
        width = int(geometry['width'])
        length = max(x_max - x_min, y_max - y_min)
        
        start_point = center - direction * length / 2
        end_point = center + direction * length / 2
        
        # 确保点在图像范围内
        start_point = np.clip(start_point, [0, 0], [area_mask.shape[1]-1, area_mask.shape[0]-1])
        end_point = np.clip(end_point, [0, 0], [area_mask.shape[1]-1, area_mask.shape[0]-1])
        
        # 绘制线段
        cv2.line(result, 
                tuple(start_point.astype(int)), 
                tuple(end_point.astype(int)), 
                1, width)
        
        # 只保留在指定区域内的部分
        result = np.logical_and(result, area_mask).astype(np.uint8)
        
        return result
    
    def _analyze_dashed_pattern(self, dashed_mask: np.ndarray) -> Optional[Dict]:
        """分析虚线模式"""
        if not np.any(dashed_mask):
            return None
        
        # 获取连通组件
        num_labels, labels = cv2.connectedComponents(dashed_mask)
        if num_labels < 2:
            return None
        
        # 分析各个虚线段
        dash_lengths = []
        dash_centers = []
        
        for label in range(1, num_labels):
            segment_mask = (labels == label)
            if np.sum(segment_mask) < 5:
                continue
            
            coords = np.where(segment_mask)
            center = (np.mean(coords[1]), np.mean(coords[0]))
            
            # 估计段长度
            contours, _ = cv2.findContours(segment_mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            if len(contours) > 0:
                rect = cv2.minAreaRect(contours[0])
                length = max(rect[1])
                dash_lengths.append(length)
                dash_centers.append(center)
        
        if len(dash_lengths) < 2:
            return None
        
        # 计算平均属性
        avg_dash_length = np.mean(dash_lengths)
        
        # 计算间隔
        if len(dash_centers) >= 2:
            distances = []
            for i in range(len(dash_centers) - 1):
                dist = np.sqrt((dash_centers[i+1][0] - dash_centers[i][0])**2 + 
                              (dash_centers[i+1][1] - dash_centers[i][1])**2)
                distances.append(dist)
            avg_gap = np.mean(distances) - avg_dash_length
        else:
            avg_gap = avg_dash_length
        
        return {
            'dash_length': max(10, avg_dash_length),
            'gap_length': max(10, avg_gap),
            'width': 3
        }
    
    def _generate_dashed_pattern_in_area(self, area_mask: np.ndarray, pattern: Dict) -> np.ndarray:
        """在指定区域内生成虚线模式"""
        result = np.zeros_like(area_mask)
        
        # 获取区域边界
        coords = np.where(area_mask)
        if len(coords[0]) == 0:
            return result
        
        y_min, y_max = np.min(coords[0]), np.max(coords[0])
        x_min, x_max = np.min(coords[1]), np.max(coords[1])
        
        # 沿水平方向生成虚线（简化）
        y_center = (y_min + y_max) // 2
        dash_length = int(pattern['dash_length'])
        gap_length = int(pattern['gap_length'])
        width = int(pattern['width'])
        
        current_x = x_min
        is_dash = True
        
        while current_x < x_max:
            if is_dash:
                end_x = min(current_x + dash_length, x_max)
                cv2.line(result, (current_x, y_center), (end_x, y_center), 1, width)
                current_x = end_x
            else:
                current_x += gap_length
            
            is_dash = not is_dash
        
        # 只保留在指定区域内的部分
        result = np.logical_and(result, area_mask).astype(np.uint8)
        
        return result
    
    def _analyze_grid_pattern(self, grid_mask: np.ndarray) -> Dict:
        """分析网格模式"""
        # 简化的网格分析
        return {'spacing': 15, 'line_width': 2}
    
    def _generate_grid_pattern_in_area(self, area_mask: np.ndarray, pattern: Dict) -> np.ndarray:
        """在指定区域内生成网格模式"""
        result = np.zeros_like(area_mask)
        
        coords = np.where(area_mask)
        if len(coords[0]) == 0:
            return result
        
        y_min, y_max = np.min(coords[0]), np.max(coords[0])
        x_min, x_max = np.min(coords[1]), np.max(coords[1])
        
        spacing = pattern['spacing']
        line_width = pattern['line_width']
        
        # 水平线
        for y in range(y_min, y_max, spacing):
            cv2.line(result, (x_min, y), (x_max, y), 1, line_width)
        
        # 垂直线
        for x in range(x_min, x_max, spacing):
            cv2.line(result, (x, y_min), (x, y_max), 1, line_width)
        
        # 只保留在指定区域内的部分
        result = np.logical_and(result, area_mask).astype(np.uint8)
        
        return result
    
    def _find_reference_direction_for_stop_line(self, lane_masks: Dict, lane_types: Dict) -> Optional[np.ndarray]:
        """为停止线寻找参考方向"""
        # 寻找实线或虚线作为参考
        for name, mask in lane_masks.items():
            if lane_types[name] in [LaneType.SOLID_LINE, LaneType.DASHED_LINE]:
                geometry = self._analyze_line_geometry(mask)
                if geometry:
                    return geometry['direction']
        return None
    
    def _apply_smoothing(self, mask: np.ndarray) -> np.ndarray:
        """应用平滑处理"""
        if self.smoothing_kernel_size <= 0:
            return mask
        
        kernel = np.ones((self.smoothing_kernel_size, self.smoothing_kernel_size), np.uint8)
        smoothed = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        return smoothed
    
    def _validate_line_geometry(self, mask: np.ndarray, expected_geometry: Dict) -> np.ndarray:
        """验证线条几何特征"""
        # 简化的几何验证
        return mask
